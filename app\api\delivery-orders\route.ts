import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, checkRelationsBeforeDelete, generateUniqueId } from '@/lib/transaction-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const deliveryOrders = await prisma.deliveryOrder.findMany({
      include: {
        items: true
      },
      orderBy: { id: 'desc' }
    });

    return NextResponse.json(deliveryOrders);
  } catch (error) {
    console.error('Failed to fetch delivery orders:', error);
    return NextResponse.json({ error: 'Failed to fetch delivery orders' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newOrder = await request.json();

    // Basic validation
    if (!newOrder.date || !newOrder.warehouseId) {
      return NextResponse.json(
        { error: 'Date and warehouse ID are required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // إنشاء رقم تسلسلي بنمط DELIV-
      let deliveryOrderNumber = newOrder.deliveryOrderNumber;

      if (!deliveryOrderNumber) {
        const existingOrders = await tx.deliveryOrder.findMany({
          select: { deliveryOrderNumber: true },
          orderBy: { id: 'desc' }
        });

        let maxNumber = 0;
        existingOrders.forEach(order => {
          const match = order.deliveryOrderNumber.match(/DELIV-(\d+)$/);
          if (match) {
            const num = parseInt(match[1]);
            if (!isNaN(num) && num > maxNumber) {
              maxNumber = num;
            }
          }
        });

        deliveryOrderNumber = `DELIV-${maxNumber + 1}`;
      } else {
        // التحقق من وجود الرقم المرسل
        const existingOrder = await tx.deliveryOrder.findUnique({
          where: { deliveryOrderNumber }
        });

        if (existingOrder) {
          // إذا كان الرقم موجوداً، نولد رقماً جديداً
          const existingOrders = await tx.deliveryOrder.findMany({
            select: { deliveryOrderNumber: true },
            orderBy: { id: 'desc' }
          });

          let maxNumber = 0;
          existingOrders.forEach(order => {
            const match = order.deliveryOrderNumber.match(/DELIV-(\d+)$/);
            if (match) {
              const num = parseInt(match[1]);
              if (!isNaN(num) && num > maxNumber) {
                maxNumber = num;
              }
            }
          });

          deliveryOrderNumber = `DELIV-${maxNumber + 1}`;
        }
      }

      // الحصول على اسم المخزن إذا لم يكن موجوداً
      let warehouseName = newOrder.warehouseName;
      if (!warehouseName && newOrder.warehouseId) {
        const warehouse = await tx.warehouse.findUnique({
          where: { id: newOrder.warehouseId }
        });
        warehouseName = warehouse ? warehouse.name : 'مخزن غير محدد';
      }

      // Create the delivery order in the database
      const order = await tx.deliveryOrder.create({
        data: {
          deliveryOrderNumber,
          referenceNumber: newOrder.referenceNumber || null,
          date: newOrder.date,
          warehouseId: newOrder.warehouseId,
          warehouseName: warehouseName || 'مخزن غير محدد',
          employeeName: newOrder.employeeName,
          notes: newOrder.notes || null,
          status: newOrder.status || 'completed',
          attachmentName: newOrder.attachmentName || null,
        }
      });

      // Update device statuses to "awaiting receipt in warehouse" first
      // The final status will be set when the devices are actually received in the warehouse
      if (newOrder.items && Array.isArray(newOrder.items)) {
        for (const item of newOrder.items) {
          await tx.device.update({
            where: { id: item.deviceId },
            data: {
              status: 'بانتظار استلام في المخزن',
              warehouseId: newOrder.warehouseId
            }
          });

          // Create maintenance log entry with proper date handling
          const repairDate = new Date(newOrder.date);
          if (isNaN(repairDate.getTime())) {
            throw new Error('Invalid date format');
          }

          await tx.maintenanceLog.create({
            data: {
              deviceId: item.deviceId,
              model: item.model,
              repairDate: repairDate.toISOString(),
              notes: item.notes || item.fault || 'تم إنهاء الصيانة',
              result: item.result,
              status: 'pending'
            }
          });
        }
      }

      
      // Create deliveryOrder items
      if (newOrder.items && Array.isArray(newOrder.items)) {
        for (const item of newOrder.items) {
          await tx.deliveryOrderItem.create({
            data: {
              deliveryOrderId: order.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              result: item.result || '',
              fault: item.fault || null,
              damage: item.damage || null,
              notes: item.notes || null
            }
          });
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created delivery order: ${deliveryOrderNumber}`
      });

      // Return order with items
      const orderWithItems = await tx.deliveryOrder.findUnique({
        where: { id: order.id },
        include: { items: true }
      });

      return orderWithItems;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to create delivery order:', error);

    if (error instanceof Error && error.message === 'Invalid date format') {
      return NextResponse.json({ error: 'Invalid date format' }, { status: 400 });
    }

    return NextResponse.json({ error: 'Failed to create delivery order' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedOrder = await request.json();

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if delivery order exists
      const existingOrder = await tx.deliveryOrder.findUnique({
        where: { id: updatedOrder.id }
      });

      if (!existingOrder) {
        throw new Error('Delivery order not found');
      }

      // Parse items to handle device status updates
      const oldItems = existingOrder.items as any;
      let oldItemsArray = [];
      try {
        oldItemsArray = typeof oldItems === 'string' ? JSON.parse(oldItems) : oldItems;
      } catch (error) {
        console.warn('Failed to parse old items:', error);
      }

      // الحصول على اسم المخزن إذا لم يكن موجوداً
      let warehouseName = updatedOrder.warehouseName;
      if (!warehouseName && updatedOrder.warehouseId) {
        const warehouse = await tx.warehouse.findUnique({
          where: { id: updatedOrder.warehouseId }
        });
        warehouseName = warehouse ? warehouse.name : 'مخزن غير محدد';
      }

      // Update the delivery order
      const order = await tx.deliveryOrder.update({
        where: { id: updatedOrder.id },
        data: {
          deliveryOrderNumber: updatedOrder.deliveryOrderNumber,
          referenceNumber: updatedOrder.referenceNumber,
          date: updatedOrder.date,
          warehouseId: updatedOrder.warehouseId,
          warehouseName: warehouseName || existingOrder.warehouseName || 'مخزن غير محدد',
          employeeName: updatedOrder.employeeName,
          notes: updatedOrder.notes,
          status: updatedOrder.status,
          attachmentName: updatedOrder.attachmentName,
        }
      });

      // Update device statuses based on maintenance result
      if (updatedOrder.items && Array.isArray(updatedOrder.items)) {
        for (const item of updatedOrder.items) {
          let finalStatus;
          switch (item.result) {
            case 'Repaired':
              finalStatus = 'متاح للبيع';
              break;
            case 'Unrepairable-Defective':
              finalStatus = 'معيب';
              break;
            case 'Unrepairable-Damaged':
              finalStatus = 'تالف';
              break;
            default:
              finalStatus = 'متاح للبيع';
          }

          await tx.device.update({
            where: { id: item.deviceId },
            data: {
              status: finalStatus,
              warehouseId: updatedOrder.warehouseId
            }
          });
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated delivery order: ${order.deliveryOrderNumber}`
      });

      // Return order with items
      const orderWithItems = await tx.deliveryOrder.findUnique({
        where: { id: order.id },
        include: { items: true }
      });

      return orderWithItems;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update delivery order:', error);

    if (error instanceof Error && error.message === 'Delivery order not found') {
      return NextResponse.json({ error: 'Delivery order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update delivery order' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if delivery order exists
      const existingOrder = await tx.deliveryOrder.findUnique({
        where: { id: parseInt(id) }
      });

      if (!existingOrder) {
        throw new Error('Delivery order not found');
      }

      // Parse items to update device statuses back
      let items = [];
      try {
        items = typeof existingOrder.items === 'string' ?
          existingOrder.items : existingOrder.items;
      } catch (error) {
        console.warn('Failed to parse items for device status update:', error);
      }

      // Update device statuses back to maintenance
      if (Array.isArray(items)) {
        for (const item of items) {
          await tx.device.update({
            where: { id: item.deviceId },
            data: { status: 'قيد الإصلاح' }
          });
        }
      }

      // Delete the delivery order
      await tx.deliveryOrder.delete({
        where: { id: parseInt(id) }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted delivery order: ${existingOrder.deliveryOrderNumber}`
      });

      return { message: 'Delivery order deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete delivery order:', error);

    if (error instanceof Error && error.message === 'Delivery order not found') {
      return NextResponse.json({ error: 'Delivery order not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete delivery order' }, { status: 500 });
  }
}