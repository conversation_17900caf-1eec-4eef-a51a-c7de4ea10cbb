
// Required API endpoints:
// - /api/users
'use client';

import { useState, useEffect } from 'react';
// TODO: Update to use API instead of store
import { useStore } from '@/context/store';
import { User, AppPermissions, permissionPages } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { UserForm } from '../../../components/user-form';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Download,
  UserPlus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Shield,
  Trash2,
  UserCheck,
  UserX,
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { exportToCSV } from '@/lib/export-utils/html-to-pdf';
import { formatArabicDate } from '@/lib/date-utils';

export default function UsersPage() {
  const { toast } = useToast();

  // Get store functions and data
  const { users, addUser, updateUser, deleteUser } = useStore();

  // State management
  const [isLoading, setIsLoading] = useState(true);

  // TODO: Add API functions here

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | undefined>(undefined);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState<(() => void) | null>(
    null
    );
  const [confirmDialogContent, setConfirmDialogContent] = useState({
    title: '',
    description: '',
  });

  const [resetPasswordUser, setResetPasswordUser] = useState<User | null>(null);
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');

  // Temporary permissions
  const permissions = { create: true, edit: true, delete: true, view: true, viewAll: true };

  // Initialize permissions for new users
  const initialPermissions: AppPermissions = permissionPages.reduce(
    (acc, page) => ({
      ...acc,
      [page]: { view: false, create: false, edit: false, delete: false },
    }),
    {} as AppPermissions
    );

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === 'all' || user.status === statusFilter;
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;

    return matchesSearch && matchesStatus && matchesRole;
  });

  // استخراج الأدوار الفريدة للفلترة
  const uniqueRoles = Array.from(
    new Set(users.map((user) => user.role).filter(Boolean))
    );

  // تحضير البيانات للتصدير
  const exportData = filteredUsers.map((user) => ({
    الاسم: user.name,
    'اسم المستخدم': user.username,
    'البريد الإلكتروني': user.email,
    الهاتف: user.phone || '-',
    'الدور الوظيفي': user.role || '-',
    'موقع الفرع': user.branchLocation || '-',
    الحالة: user.status || 'Active',
    'آخر دخول': user.lastLogin ? formatArabicDate(user.lastLogin) : '-',
  }));

  const exportHeaders = [
    'الاسم',
    'اسم المستخدم',
    'البريد الإلكتروني',
    'الهاتف',
    'الدور الوظيفي',
    'موقع الفرع',
    'الحالة',
    'آخر دخول',
  ];

  const handleExportUsers = () => {
    exportToCSV(exportData, exportHeaders, 'users-report');
    toast({
      title: 'تم التصدير بنجاح',
      description: 'تم تصدير بيانات المستخدمين إلى ملف CSV',
    });
  };

  const handleAddUser = () => {
    setEditingUser(undefined);
    setIsFormOpen(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setIsFormOpen(true);
  };

  const handleSaveUser = (user: User, password?: string) => {
    try {
      if (user.id && user.id !== 0) {
        updateUser(user);
      } else {
        const { id, ...newUser } = user;
        addUser({
          ...newUser,
          status: 'Active',
          permissions: user.permissions || initialPermissions,
        });
      }
      setIsFormOpen(false);
      // رسالة النجاح ستظهر من UserForm
    } catch (error) {
      console.error('Error saving user:', error);
      // رسالة الخطأ ستظهر من UserForm
      throw error; // إعادة رمي الخطأ ليتم التعامل معه في UserForm
    }
  };

  const openConfirmationDialog = (
    title: string,
    description: string,
    action: () => void,
  ) => {
    setConfirmDialogContent({ title, description });
    setConfirmAction(() => action);
    setIsConfirmDialogOpen(true);
  };

  const handleDisableUser = (userToUpdate: User) => {
    updateUser({
      ...userToUpdate,
      status: userToUpdate.status === 'Active' ? 'Inactive' : 'Active',
    });
    toast({
      title: 'تم تحديث حالة المستخدم',
      description: 'تم تحديث حالة المستخدم بنجاح',
    });
  };

  const handleResetPasswordConfirm = () => {
    if (!resetPasswordUser) return;
    if (!newPassword || newPassword !== confirmNewPassword) {
      toast({
        title: 'خطأ',
        description: 'كلمتا المرور غير متطابقتين أو فارغتان.',
        variant: 'destructive',
      });
      return;
    }
    // In a real app, you would make an API call here.
    // For now, we simulate success.
    console.log(
      `Password for ${resetPasswordUser.name} would be changed to ${newPassword}`
    );

    toast({
      title: 'تم إعادة التعيين',
      description: `تمت إعادة تعيين كلمة المرور للمستخدم ${resetPasswordUser.name} بنجاح.`,
    });
    setResetPasswordUser(null);
    setNewPassword('');
    setConfirmNewPassword('');
  };

  return (
    <div className="container mx-auto p-4" dir="rtl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إدارة المستخدمين</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportUsers}>
            <Download className="ml-2 h-4 w-4" />
            تصدير البيانات
          </Button>
          <Button onClick={handleAddUser}>
            <UserPlus className="ml-2 h-4 w-4" />
            إضافة مستخدم جديد
          </Button>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              إجمالي المستخدمين
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{users.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">نشط</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {users.filter((u) => u.status === 'Active').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">غير نشط</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {users.filter((u) => u.status === 'Inactive').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              الأدوار المختلفة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{uniqueRoles.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* فلاتر البحث */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            فلاتر البحث
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="بحث بالاسم, اسم المستخدم, أو البريد..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="فلترة حسب الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="Active">نشط</SelectItem>
                <SelectItem value="Inactive">غير نشط</SelectItem>
              </SelectContent>
            </Select>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger>
                <SelectValue placeholder="فلترة حسب الدور" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأدوار</SelectItem>
                {uniqueRoles.map((role) =>
                  role ? (
                    <SelectItem key={role} value={role}>
                      {role}
                    </SelectItem>
                  ) : null,
                )}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>الاسم</TableHead>
              <TableHead>اسم المستخدم</TableHead>
              <TableHead>البريد الإلكتروني</TableHead>
              <TableHead>الهاتف</TableHead>
              <TableHead>الدور/المسمى</TableHead>
              <TableHead>موقع الفرع</TableHead>
              <TableHead>الحالة</TableHead>
              <TableHead>آخر دخول</TableHead>
              <TableHead className="text-center">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">{user.name}</TableCell>
                <TableCell>{user.username}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>{user.phone || '-'}</TableCell>
                <TableCell>{user.role || '-'}</TableCell>
                <TableCell>{user.branchLocation || '-'}</TableCell>
                <TableCell>
                  <Badge
                    variant={user.status === 'Active' ? 'default' : 'destructive'}
                  >
                    {user.status === 'Active' ? 'نشط' : 'غير نشط'}
                  </Badge>
                </TableCell>
                <TableCell>
                  {user.lastLogin ? formatArabicDate(user.lastLogin) : '-'}
                </TableCell>
                <TableCell className="text-center">
                  <div className="flex items-center justify-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditUser(user)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setResetPasswordUser(user)}
                    >
                      <Shield className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={
                        user.status === 'Active' ? 'destructive' : 'default'
                      }
                      size="sm"
                      onClick={() => handleDisableUser(user)}
                      disabled={user.id === 1}
                    >
                      {user.status === 'Inactive' ? (
                        <UserCheck className="h-4 w-4" />
                      ) : (
                        <UserX className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <UserForm
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSave={handleSaveUser}
        currentUser={editingUser}
      />

      <Dialog
        open={!!resetPasswordUser}
        onOpenChange={() => setResetPasswordUser(null)}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              إعادة تعيين كلمة المرور لـ {resetPasswordUser?.name}
            </DialogTitle>
            <DialogDescription>
              أدخل كلمة المرور الجديدة وقم بتأكيدها.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="new-password">كلمة المرور الجديدة</Label>
              <Input
                id="new-password"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirm-new-password">تأكيد كلمة المرور</Label>
              <Input
                id="confirm-new-password"
                type="password"
                value={confirmNewPassword}
                onChange={(e) => setConfirmNewPassword(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setResetPasswordUser(null)}
            >
              إلغاء
            </Button>
            <Button onClick={handleResetPasswordConfirm}>
              إعادة تعيين
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
