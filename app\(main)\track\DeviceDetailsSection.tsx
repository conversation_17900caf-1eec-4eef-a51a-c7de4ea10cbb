'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Smartphone,
  MapPin,
  Star,
  Calendar,
  Package,
  ClipboardCheck,
  Wrench,
  ShieldCheck,
  User,
  Building,
  Barcode,
  Activity
} from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface DeviceDetailsSectionProps {
  device: any;
  searchedImei: string;
  customerViewDetails: any;
  evaluationOrders: any[];
  supplyOrders: any[];
  suppliers: any[];
  warehouseTransfers: any[];
}

// دالة مساعدة لتنسيق التاريخ بالعربية
function formatArabicDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

// دالة للحصول على لون الحالة
function getStatusColor(status: string): string {
  switch (status) {
    case 'متاح':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'مباع':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'قيد الإصلاح':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'معطل':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'مرتجع':
      return 'bg-orange-100 text-orange-800 border-orange-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
}

// دالة للحصول على أيقونة الحالة
function getStatusIcon(status: string) {
  switch (status) {
    case 'متاح':
      return <Package className="h-4 w-4" />;
    case 'مباع':
      return <User className="h-4 w-4" />;
    case 'قيد الإصلاح':
      return <Wrench className="h-4 w-4" />;
    case 'معطل':
      return <Activity className="h-4 w-4" />;
    case 'مرتجع':
      return <Package className="h-4 w-4" />;
    default:
      return <Package className="h-4 w-4" />;
  }
}

export default function DeviceDetailsSection({
  device,
  searchedImei,
  customerViewDetails,
  evaluationOrders,
  supplyOrders,
  suppliers,
  warehouseTransfers
}: DeviceDetailsSectionProps) {
  
  // الحصول على آخر تقييم للجهاز
  const latestEvaluation = evaluationOrders
    .filter(order => Array.isArray(order.items) && order.items.some(item => item.deviceId === searchedImei))
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];
  
  const evaluatedItem = latestEvaluation?.items?.find(item => item.deviceId === searchedImei);

  // الحصول على معلومات التوريد
  const supplyOrder = supplyOrders.find(so => 
    Array.isArray(so.items) && so.items.some(item => item.imei === searchedImei)
  );
  const supplier = supplyOrder ? suppliers.find(s => s.id === supplyOrder.supplierId) : null;

  // الحصول على آخر موقع للجهاز
  const getDeviceLocation = () => {
    // البحث في التحويلات المخزنية
    const latestTransfer = warehouseTransfers
      .filter(transfer => Array.isArray(transfer.items) && transfer.items.some(item => item.deviceId === searchedImei))
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];
    
    if (latestTransfer && latestTransfer.status === 'completed') {
      return latestTransfer.toWarehouseName;
    }
    
    // إذا لم يكن هناك تحويل، استخدم مخزن التوريد
    if (supplyOrder && supplyOrder.warehouseId) {
      return `مخزن رقم ${supplyOrder.warehouseId}`;
    }
    
    return 'غير محدد';
  };

  return (
    <Card className="device-details-card border-0 shadow-lg bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-900/20 dark:to-gray-900/20">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Smartphone className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <CardTitle className="text-lg font-bold text-gray-800">
              معلومات الجهاز
            </CardTitle>
            <CardDescription className="text-gray-600 text-sm">
              تفاصيل شاملة وحالة حالية
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">

          {/* المعلومات الأساسية */}
          <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex items-center gap-2 mb-1">
              <Smartphone className="h-3 w-3 text-blue-600" />
              <span className="font-medium text-gray-700 text-xs">الموديل</span>
            </div>
            <p className="text-gray-900 font-medium text-sm">{device.model}</p>
          </div>

          <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex items-center gap-2 mb-1">
              <Barcode className="h-3 w-3 text-gray-600" />
              <span className="font-medium text-gray-700 text-xs">الرقم التسلسلي</span>
            </div>
            <p className="text-gray-900 font-mono text-xs break-all">{searchedImei}</p>
          </div>

          <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex items-center gap-2 mb-1">
              <Activity className="h-3 w-3 text-green-600" />
              <span className="font-medium text-gray-700 text-xs">الحالة الحالية</span>
            </div>
            <Badge className={`${getStatusColor(device.status)} border px-2 py-1 text-xs`}>
              {getStatusIcon(device.status)}
              <span className="mr-1">{device.status}</span>
            </Badge>
          </div>

          {/* معلومات التقييم */}
          {evaluatedItem ? (
            <>
              <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center gap-2 mb-1">
                  <Star className="h-3 w-3 text-yellow-600" />
                  <span className="font-medium text-gray-700 text-xs">التقييم النهائي</span>
                </div>
                <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 px-2 py-1 text-xs">
                  {evaluatedItem.finalGrade}
                </Badge>
              </div>

              <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-center gap-2 mb-1">
                  <ClipboardCheck className="h-3 w-3 text-indigo-600" />
                  <span className="font-medium text-gray-700 text-xs">درجات التقييم</span>
                </div>
                <div className="text-xs space-y-1">
                  <div>خارجي: <Badge variant="outline" className="text-xs">{evaluatedItem.externalGrade}</Badge></div>
                  <div>شاشة: <Badge variant="outline" className="text-xs">{evaluatedItem.screenGrade}</Badge></div>
                  <div>شبكة: <Badge variant="outline" className="text-xs">{evaluatedItem.networkGrade}</Badge></div>
                </div>
              </div>

              {(evaluatedItem.fault || evaluatedItem.damageType) && (
                <div className="bg-red-50 p-3 rounded-lg border border-red-200 shadow-sm">
                  <div className="flex items-center gap-2 mb-1">
                    <Activity className="h-3 w-3 text-red-600" />
                    <span className="font-medium text-red-700 text-xs">الأعطال</span>
                  </div>
                  <p className="text-red-800 text-xs">
                    {evaluatedItem.fault || evaluatedItem.damageType}
                  </p>
                </div>
              )}
            </>
          ) : (
            <div className="bg-gray-50 p-3 rounded-lg border border-gray-200 text-center">
              <ClipboardCheck className="h-4 w-4 text-gray-400 mx-auto mb-1" />
              <p className="text-gray-500 text-xs">لم يتم التقييم</p>
            </div>
          )}

          {/* معلومات الموقع والتوريد */}
          <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex items-center gap-2 mb-1">
              <MapPin className="h-3 w-3 text-green-600" />
              <span className="font-medium text-gray-700 text-xs">الموقع الحالي</span>
            </div>
            <p className="text-gray-900 font-medium text-sm">{getDeviceLocation()}</p>
          </div>

          {supplier && (
            <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center gap-2 mb-1">
                <Building className="h-3 w-3 text-purple-600" />
                <span className="font-medium text-gray-700 text-xs">المورد</span>
              </div>
              <p className="text-gray-900 font-medium text-sm">{supplier.name}</p>
            </div>
          )}

          {supplyOrder && (
            <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center gap-2 mb-1">
                <Package className="h-3 w-3 text-cyan-600" />
                <span className="font-medium text-gray-700 text-xs">أمر التوريد</span>
              </div>
              <p className="text-gray-900 font-medium text-sm">{supplyOrder.supplyOrderId}</p>
            </div>
          )}

          {/* معلومات الضمان للعرض العام */}
          {customerViewDetails?.warrantyInfo && (
            <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center gap-2 mb-1">
                <ShieldCheck className="h-3 w-3 text-blue-600" />
                <span className="font-medium text-gray-700 text-xs">حالة الضمان</span>
              </div>
              <Badge className={`${
                customerViewDetails.warrantyInfo.status === 'في الضمان' ? 'bg-green-100 text-green-800 border-green-200' :
                customerViewDetails.warrantyInfo.status === 'ضمان منتهي' ? 'bg-red-100 text-red-800 border-red-200' :
                'bg-gray-100 text-gray-800 border-gray-200'
              } border px-2 py-1 text-xs`}>
                {customerViewDetails.warrantyInfo.status}
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
