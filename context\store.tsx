"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import {
  Device,
  Contact,
  Warehouse,
  Sale,
  SaleItem,
  Return,
  DeviceStatus,
  Manufacturer,
  DeviceModel,
  SupplyOrder,
  EvaluationOrder,
  MaintenanceLog,
  MaintenanceResult,
  WarehouseTransfer,
  User,
  SystemSettings,
  EmployeeRequest,
  EmployeeRequestStatus,
  InternalMessage,
  MessageStatus,
  AcceptanceOrder,
  MaintenanceOrder,
  DeliveryOrder,
  DeliveryOrderItem,
  MaintenanceReceiptOrder,
  DeviceReturnHistory,
  Stocktake,
  StocktakeDiscrepancy,
  StocktakeStatus,
  StocktakeFilter,
  StocktakeV1, // Import StocktakeV1
  StocktakeItemV1, // Import StocktakeItemV1
  AppPermissions,
  permissionPages,
} from "@/lib/types";
import {
  initialDevices,
  initialClients,
  initialSuppliers,
  initialWarehouses,
  initialSales,
  initialReturns,
  initialManufacturers,
  initialDeviceModels,
  initialSupplyOrders,
  initialEvaluationOrders,
  initialWarehouseTransfers,
  initialUsers,
  initialSystemSettings,
  initialEmployeeRequests,
  initialInternalMessages,
  initialAcceptanceOrders,
  initialMaintenanceOrders,
  initialDeliveryOrders,
} from "@/lib/data";
import type { ActivityLog } from "@/lib/types";
import { apiClient, handleApiResponse } from "@/lib/api-client";

interface StoreContextType {
  isLoading: boolean;
  devices: Device[];
  clients: Contact[];
  suppliers: Contact[];
  warehouses: Warehouse[];
  sales: Sale[];
  returns: Return[];
  activities: ActivityLog[];
  manufacturers: Manufacturer[];
  deviceModels: DeviceModel[];
  supplyOrders: SupplyOrder[];
  evaluationOrders: EvaluationOrder[];
  maintenanceHistory: MaintenanceLog[];
  warehouseTransfers: WarehouseTransfer[];
  users: User[];
  currentUser: User | null;
  systemSettings: SystemSettings;
  employeeRequests: EmployeeRequest[];
  internalMessages: InternalMessage[];
  acceptanceOrders: AcceptanceOrder[];
  maintenanceOrders: MaintenanceOrder[];
  deliveryOrders: DeliveryOrder[];
  maintenanceReceiptOrders: MaintenanceReceiptOrder[];
  deviceReturnHistory: DeviceReturnHistory[];
  stocktakes: StocktakeV1[];

  // Device functions
  addDevice: (device: Omit<Device, "id"> & { id?: string }) => Promise<void>;
  updateDevice: (device: Device) => void;
  deleteDevice: (deviceId: string) => void;
  updateDeviceStatus: (deviceId: string, status: DeviceStatus) => void;
  getDevicesByStatus: (status: DeviceStatus) => Device[];
  getDevicesByIds: (ids: string[]) => Device[];

  // Contact functions
  addContact: (contact: Omit<Contact, "id">, type: "client" | "supplier") => Promise<void>;
  updateContact: (contact: Contact, type: "client" | "supplier") => Promise<void>;
  deleteContact: (contactId: number, type: "client" | "supplier") => Promise<void>;
  checkClientRelations: (clientId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };
  checkSupplierRelations: (supplierId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };

  // Warehouse functions
  addWarehouse: (warehouse: Omit<Warehouse, "id">) => Promise<void>;
  updateWarehouse: (warehouse: Warehouse) => Promise<void>;
  deleteWarehouse: (warehouseId: number) => Promise<void>;
  checkWarehouseRelations: (warehouseId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };

  // Sales functions
  addSale: (sale: Omit<Sale, "id" | "saleNumber">) => Promise<void>;
  updateSale: (sale: Sale) => void;
  deleteSale: (saleId: number) => void;
  checkSaleRelations: (saleId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };

  // Return functions
  addReturn: (returnOrder: Omit<Return, "id" | "returnNumber">) => Promise<void>;
  updateReturn: (returnOrder: Return) => Promise<void>;
  deleteReturn: (returnId: number) => Promise<void>;
  isDeviceReturned: (deviceId: string) => boolean;
  canDeviceBeReturned: (deviceId: string) => { canReturn: boolean; reason?: string };
  addDeviceReturnHistory: (history: Omit<DeviceReturnHistory, "deviceId"> & { deviceId: string }) => void;

  // Manufacturer and model functions
  addManufacturer: (name: string) => Manufacturer;
  addDeviceModel: (model: Omit<DeviceModel, "id">) => Promise<void>;

  // Supply order functions
  addSupplyOrder: (order: Omit<SupplyOrder, "id" | "createdAt">) => Promise<void>;
  updateSupplyOrder: (order: SupplyOrder) => Promise<void>;
  deleteSupplyOrder: (orderId: number) => Promise<void>;
  checkSupplyOrderRelations: (orderId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };

  // Evaluation order functions
  addEvaluationOrder: (order: Omit<EvaluationOrder, "id" | "orderNumber">) => void;
  updateEvaluationOrder: (order: EvaluationOrder) => void;
  deleteEvaluationOrder: (orderId: number) => void;
  checkEvaluationOrderRelations: (orderId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };

  // Maintenance functions
  addMaintenanceLog: (log: Omit<MaintenanceLog, "id">) => void;
  acknowledgeMaintenanceLog: (deviceId: string) => void;
  revertDeviceToMaintenance: (deviceId: string) => void;

  // Warehouse transfer functions
  addWarehouseTransfer: (transfer: Omit<WarehouseTransfer, "id" | "transferNumber">) => void;
  updateWarehouseTransfer: (transfer: WarehouseTransfer) => void;
  deleteWarehouseTransfer: (transferId: number) => void;
  completeWarehouseTransfer: (transferId: number) => void;

  // User functions
  addUser: (user: Omit<User, "id">) => Promise<void>;
  updateUser: (user: User) => Promise<void>;
  deleteUser: (userId: number) => Promise<void>;
  setCurrentUser: (user: User | null) => void;

  // System settings
  updateSystemSettings: (settings: SystemSettings) => void;

  // Employee requests
  addEmployeeRequest: (request: Omit<EmployeeRequest, "id" | "requestNumber">) => void;
  processEmployeeRequest: (requestId: number, status: EmployeeRequestStatus, processedBy: number, notes?: string) => void;

  // Internal messages
  sendMessage: (message: Omit<InternalMessage, "id" | "messageNumber">) => void;
  updateMessage: (messageId: number, status: MessageStatus) => void;

  // Acceptance orders
  addAcceptanceOrder: (order: Omit<AcceptanceOrder, "id" | "acceptanceId">) => void;
  updateAcceptanceOrder: (order: AcceptanceOrder) => void;
  deleteAcceptanceOrder: (orderId: number) => void;

  // Maintenance orders
  addMaintenanceOrder: (order: Omit<MaintenanceOrder, "id" | "createdAt"> & { id?: number; status?: "wip" | "completed" | "draft" }) => Promise<MaintenanceOrder>;
  updateMaintenanceOrder: (order: MaintenanceOrder) => void;
  deleteMaintenanceOrder: (orderId: number) => void;
  checkMaintenanceOrderRelations: (orderId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };
  checkDeviceRelationsInMaintenance: (deviceId: string, currentMaintenanceOrderId?: number) => { hasRelations: boolean; relatedOperations: string[] };

  // Delivery orders
  addDeliveryOrder: (order: Omit<DeliveryOrder, "id" | "createdAt">) => Promise<DeliveryOrder>;
  updateDeliveryOrder: (order: DeliveryOrder) => Promise<DeliveryOrder>;
  deleteDeliveryOrder: (orderId: number) => Promise<void>;
  checkDeliveryOrderRelations: (orderId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };

  // Maintenance receipt orders
  addMaintenanceReceiptOrder: (order: Omit<MaintenanceReceiptOrder, "id" | "createdAt">) => Promise<MaintenanceReceiptOrder>;
  updateMaintenanceReceiptOrder: (order: MaintenanceReceiptOrder) => Promise<MaintenanceReceiptOrder>;
  deleteMaintenanceReceiptOrder: (orderId: number) => Promise<void>;

  // Stocktake functions
  addStocktake: (stocktake: Omit<StocktakeV1, "id" | "operationNumber" | "createdAt" | "lastModifiedAt">) => Promise<StocktakeV1>;
  updateStocktake: (stocktake: StocktakeV1) => Promise<StocktakeV1>;
  deleteStocktake: (stocktakeId: number) => Promise<void>;
  getFilteredStocktakes: () => StocktakeV1[];
  addStocktakeItem: (stocktakeId: number, item: StocktakeItemV1) => void;
  updateStocktakeItem: (stocktakeId: number, deviceId: string, updates: Partial<StocktakeItemV1>) => void;
  addStocktakeDiscrepancy: (stocktakeId: number, discrepancy: Omit<StocktakeDiscrepancy, "resolved" | "resolvedBy" | "resolvedAt">) => void;
  resolveStocktakeDiscrepancy: (stocktakeId: number, discrepancyIndex: number, resolutionNotes: string, resolvedBy: number) => void;
  changeStocktakeStatus: (stocktakeId: number, status: StocktakeStatus) => void;
  reviewStocktake: (stocktakeId: number, reviewedBy: number, reviewedByName: string, reviewNotes: string, approved: boolean) => void;

  // Backup functions
  createBackupSnapshot: () => any;
  restoreFromSnapshot: (snapshot: any) => void;
  exportStoreData: () => any;
  importStoreData: (data: any) => void;
  
  // Auth functions
  getAuthHeader: () => { Authorization: string };
}

const StoreContext = createContext<StoreContextType | undefined>(undefined);

export function StoreProvider({ children }: { children: ReactNode }) {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [devices, setDevices] = useState<Device[]>([]);
  const [clients, setClients] = useState<Contact[]>(initialClients);
  const [suppliers, setSuppliers] = useState<Contact[]>(initialSuppliers);
  const [warehouses, setWarehouses] = useState<Warehouse[]>(
    initialWarehouses || [],
  );
  const [sales, setSales] = useState<Sale[]>([]);
  const [returns, setReturns] = useState<Return[]>(initialReturns);
  const [activities, setActivities] = useState<ActivityLog[]>([]);
  const [deviceReturnHistory, setDeviceReturnHistory] = useState<
    DeviceReturnHistory[]
  >([]);
  const [manufacturers, setManufacturers] =
    useState<Manufacturer[]>(initialManufacturers);
  const [deviceModels, setDeviceModels] =
    useState<DeviceModel[]>(initialDeviceModels);
  const [supplyOrders, setSupplyOrders] = useState<SupplyOrder[]>([]);

  // تحديث أوامر التوريد الموجودة لإضافة createdAt إذا لم تكن موجودة
  useEffect(() => {
    setSupplyOrders((prevOrders) =>
      prevOrders.map((order) => ({
        ...order,
        createdAt: order.createdAt || order.supplyDate + "T00:00:00.000Z",
      })),
    );
  }, []);
  const [evaluationOrders, setEvaluationOrders] = useState<EvaluationOrder[]>(
    initialEvaluationOrders,
  );
  const [maintenanceHistory, setMaintenanceHistory] = useState<
    MaintenanceLog[]
  >([]);
  const [warehouseTransfers, setWarehouseTransfers] = useState<
    WarehouseTransfer[]
  >(initialWarehouseTransfers);
  const [users, setUsers] = useState<User[]>(initialUsers || []);
  const [currentUser, setCurrentUser] = useState<User | null>(
    initialUsers?.[0] || null,
  );
  const [systemSettings, setSystemSettings] = useState<SystemSettings>(
    initialSystemSettings,
  );
  const [employeeRequests, setEmployeeRequests] = useState<EmployeeRequest[]>(
    initialEmployeeRequests,
  );
  const [internalMessages, setInternalMessages] = useState<InternalMessage[]>(
    initialInternalMessages,
  );
  const [acceptanceOrders, setAcceptanceOrders] = useState<AcceptanceOrder[]>(
    initialAcceptanceOrders,
  );
  const [maintenanceOrders, setMaintenanceOrders] = useState<
    MaintenanceOrder[]
  >(initialMaintenanceOrders);
  const [deliveryOrders, setDeliveryOrders] = useState<DeliveryOrder[]>(
    initialDeliveryOrders,
  );
  const [maintenanceReceiptOrders, setMaintenanceReceiptOrders] = useState<
    MaintenanceReceiptOrder[]
  >([]);
  const [stocktakes, setStocktakes] = useState<StocktakeV1[]>([]);

  // Load state from API on initial render instead of localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Load data from APIs instead of localStorage
      loadDataFromAPIs();
    }
  }, []);

  // Function to load data from APIs
  const loadDataFromAPIs = async () => {
    try {
      setIsLoading(true);
      console.log("بدء تحميل بيانات التطبيق من API...");

      // Load users
      try {
        console.log("جاري تحميل بيانات المستخدمين...");
        const usersResponse = await apiClient.get("/api/users");
        const usersData = await handleApiResponse(usersResponse);
        console.log("تم تحميل المستخدمين:", usersData);
        if (Array.isArray(usersData) && usersData.length > 0) {
          setUsers(usersData);

          // إنشاء صلاحيات كاملة للمستخدم الأول إذا لم تكن موجودة
          const firstUser = usersData[0];
          if (!firstUser.permissions) {
            const permissions = {} as AppPermissions;
            permissionPages.forEach((page) => {
              permissions[page] = {
                view: true,
                create: true,
                edit: true,
                delete: true,
                viewAll: true,
                manage: [1, 2, 3],
                acceptWithoutWarranty: true,
              };
            });
            firstUser.permissions = permissions;
          }

          setCurrentUser(firstUser);
          console.log("تم تعيين المستخدم الحالي:", firstUser);
        } else {
          console.warn("لم يتم العثور على مستخدمين");

          // إنشاء مستخدم افتراضي إذا لم يكن هناك مستخدمين
          const defaultUser = {
            id: 1,
            name: "مدير النظام",
            username: "admin",
            email: "<EMAIL>",
            permissions: {} as AppPermissions,
          };

          // إضافة صلاحيات كاملة للمستخدم الافتراضي
          permissionPages.forEach((page) => {
            defaultUser.permissions[page] = {
              view: true,
              create: true,
              edit: true,
              delete: true,
              viewAll: true,
              manage: [1, 2, 3],
              acceptWithoutWarranty: true,
            };
          });

          setUsers([defaultUser]);
          setCurrentUser(defaultUser);
          console.log("تم إنشاء مستخدم افتراضي:", defaultUser);
        }
      } catch (error) {
        console.error("خطأ في تحميل المستخدمين:", error);
        setUsers(initialUsers);
        setCurrentUser(initialUsers[0]);
      }

      // Load other data with auth headers...
      try {
        const [
          devicesRes,
          warehousesRes,
          supplyRes,
          salesRes,
          returnsRes,
          clientsRes,
          suppliersRes,
          deliveryOrdersRes,
          deviceModelsRes,
          maintenanceOrdersRes,
          maintenanceReceiptsRes,
          evaluationsRes,
          internalMessagesRes
        ] = await Promise.all([
          apiClient.get("/api/devices"),
          apiClient.get("/api/warehouses"),
          apiClient.get("/api/supply"),
          apiClient.get("/api/sales"),
          apiClient.get("/api/returns"),
          apiClient.get("/api/clients"),
          apiClient.get("/api/suppliers"),
          apiClient.get("/api/delivery-orders"),
          apiClient.get("/api/device-models"),
          apiClient.get("/api/maintenance-orders"),
          apiClient.get("/api/maintenance-receipts"),
          apiClient.get("/api/evaluations"),
          apiClient.get("/api/internal-messages")
        ]);

        const [
          devicesData,
          warehousesData,
          supplyData,
          salesData,
          returnsData,
          clientsData,
          suppliersData,
          deliveryOrdersData,
          deviceModelsData,
          maintenanceOrdersData,
          maintenanceReceiptsData,
          evaluationsData,
          internalMessagesData
        ] = await Promise.all([
          handleApiResponse(devicesRes),
          handleApiResponse(warehousesRes),
          handleApiResponse(supplyRes),
          handleApiResponse(salesRes),
          handleApiResponse(returnsRes),
          handleApiResponse(clientsRes),
          handleApiResponse(suppliersRes),
          handleApiResponse(deliveryOrdersRes),
          handleApiResponse(deviceModelsRes),
          handleApiResponse(maintenanceOrdersRes),
          handleApiResponse(maintenanceReceiptsRes),
          handleApiResponse(evaluationsRes),
          handleApiResponse(internalMessagesRes)
        ]);

        // Set data
        if (Array.isArray(devicesData)) setDevices(devicesData);
        if (Array.isArray(warehousesData)) setWarehouses(warehousesData);
        if (Array.isArray(supplyData)) setSupplyOrders(supplyData);
        if (Array.isArray(salesData)) setSales(salesData);
        if (Array.isArray(returnsData)) setReturns(returnsData);
        if (Array.isArray(clientsData)) setClients(clientsData);
        if (Array.isArray(suppliersData)) setSuppliers(suppliersData);
        if (Array.isArray(deliveryOrdersData)) setDeliveryOrders(deliveryOrdersData);
        if (Array.isArray(deviceModelsData)) setDeviceModels(deviceModelsData);
        if (Array.isArray(maintenanceOrdersData)) {
          // تحويل items من JSON string إلى array
          const processedMaintenanceOrders = maintenanceOrdersData.map((order: any) => ({
            ...order,
            items: order.items ? (typeof order.items === 'string' ? JSON.parse(order.items) : order.items) : []
          }));
          setMaintenanceOrders(processedMaintenanceOrders);
        }
        if (Array.isArray(maintenanceReceiptsData)) {
          // تحويل items من JSON string إلى array
          const processedMaintenanceReceipts = maintenanceReceiptsData.map((order: any) => ({
            ...order,
            items: order.items ? (typeof order.items === 'string' ? JSON.parse(order.items) : order.items) : []
          }));
          setMaintenanceReceiptOrders(processedMaintenanceReceipts);
        }
        if (Array.isArray(evaluationsData)) {
          console.log("تم تحميل أوامر التقييم:", evaluationsData.length);
          setEvaluationOrders(evaluationsData);
        }
        if (Array.isArray(internalMessagesData)) {
          console.log("تم تحميل المراسلات الداخلية:", internalMessagesData.length);
          setInternalMessages(internalMessagesData);
        }

        // Load employee requests
        try {
          console.log("جاري تحميل طلبات الموظفين...");
          const employeeRequestsResponse = await apiClient.get("/api/employee-requests", {
            headers: getAuthHeader()
          });
          if (employeeRequestsResponse.ok) {
            const employeeRequestsData = await employeeRequestsResponse.json();
            console.log("تم تحميل طلبات الموظفين:", employeeRequestsData);
            setEmployeeRequests(employeeRequestsData);
          }
        } catch (error) {
          console.error("خطأ في تحميل طلبات الموظفين:", error);
        }



        // Load system settings
        try {
          console.log("جاري تحميل إعدادات النظام...");
          const settingsResponse = await apiClient.get("/api/settings");
          if (settingsResponse.ok) {
            const settingsData = await settingsResponse.json();
            console.log("تم تحميل إعدادات النظام:", settingsData);
            setSystemSettings(settingsData);
          }
        } catch (error) {
          console.error("خطأ في تحميل إعدادات النظام:", error);
        }

        console.log("تم تحميل جميع البيانات بنجاح");
      } catch (error) {
        console.error("خطأ في تحميل البيانات:", error);
        // Use initial data as fallback
      }

    } catch (error) {
      console.error("خطأ عام في تحميل البيانات:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // تحديث البيانات القديمة لإضافة createdAt
  useEffect(() => {
    setMaintenanceOrders((prevOrders) =>
      prevOrders.map((order) => ({
        ...order,
        createdAt: order.createdAt || order.date + "T00:00:00.000Z",
      })),
    );

    setMaintenanceReceiptOrders((prevOrders) =>
      prevOrders.map((order) => ({
        ...order,
        createdAt: order.createdAt || order.date + "T00:00:00.000Z",
      })),
    );

    setDeliveryOrders((prevOrders) =>
      prevOrders.map((order) => ({
        ...order,
        createdAt: order.createdAt || order.date + "T00:00:00.000Z",
      })),
    );

    setEvaluationOrders((prevOrders) =>
      prevOrders.map((order) => ({
        ...order,
        createdAt: order.createdAt || order.date + "T00:00:00.000Z",
      })),
    );
  }, []);

  // Remove auto-save to localStorage - data should persist in database
  // useEffect(() => {
  //   if (typeof window !== 'undefined') {
  //     const backup = createBackupSnapshot();
  //     localStorage.setItem('appData', JSON.stringify(backup));
  //   }
  // }, [
  //   devices,
  //   clients,
  //   suppliers,
  //   warehouses,
  //   sales,
  //   returns,
  //   activities,
  //   deviceReturnHistory,
  //   manufacturers,
  //   deviceModels,
  //   supplyOrders,
  //   evaluationOrders,
  //   maintenanceHistory,
  //   warehouseTransfers,
  //   users,
  //   currentUser,
  //   systemSettings,
  //   employeeRequests,
  //   internalMessages,
  //   acceptanceOrders,
  //   maintenanceOrders,
  //   deliveryOrders,
  //   maintenanceReceiptOrders,
  //   stocktakes,
  // ]);

  const addActivity = (log: Omit<ActivityLog, "id" | "date" | "username">) => {
    if (!currentUser) return;
    const newActivity: ActivityLog = {
      ...log,
      id: `act-${Date.now()}`,
      date: new Date(),
      username: currentUser.name,
    };
    setActivities((prev) => [newActivity, ...prev]);
  };

  const updateDeviceStatus = async (deviceId: string, status: DeviceStatus) => {
    try {
      // تحديث الحالة محلياً أولاً للاستجابة السريعة
      setDevices((prev) =>
        prev.map((d) => (d.id === deviceId ? { ...d, status } : d)),
      );

      // تحديث المخازن عند تغيير حالة الجهاز
      const device = devices.find(d => d.id === deviceId);
      if (device && device.warehouseId) {
        // إعادة حساب المخزون للمخزن المرتبط بالجهاز
        const warehouseDevices = devices.filter(d => d.warehouseId === device.warehouseId);
        const totalDevices = warehouseDevices.length;
        const availableDevices = warehouseDevices.filter(d =>
          d.status === 'متاح للبيع' || d.status === 'جديد'
        ).length;

        setWarehouses(prev =>
          prev.map(warehouse =>
            warehouse.id === device.warehouseId
              ? {
                  ...warehouse,
                  totalDevices,
                  availableDevices,
                  lastUpdated: new Date().toISOString()
                }
              : warehouse
          )
        );
      }

      // حفظ التحديث في قاعدة البيانات
      if (device) {
        const response = await apiClient.put('/api/devices', {
          ...device,
          status: status
        });

        if (!response.ok) {
          // إذا فشل الحفظ، نعيد الحالة المحلية للحالة السابقة
          setDevices((prev) =>
            prev.map((d) => (d.id === deviceId ? { ...d, status: device.status } : d)),
          );
          throw new Error('Failed to update device status in database');
        }

        // تسجيل النشاط
        addActivity({
          type: "device",
          description: `تم تحديث حالة الجهاز ${deviceId} إلى ${status}`,
        });
      }
    } catch (error) {
      console.error('Failed to update device status:', error);
      addActivity({
        type: "device",
        description: `⚠️ فشل في تحديث حالة الجهاز ${deviceId}: ${error instanceof Error ? error.message : String(error)}`,
      });
    }
  };

  const getDevicesByStatus = (status: DeviceStatus) => {
    return devices.filter((d) => d.status === status);
  };

  // وظائف التحقق من الأجهزة المرتجعة
  const isDeviceReturned = (deviceId: string): boolean => {
    return deviceReturnHistory.some(
      (history) =>
        history.deviceId === deviceId && history.status === "returned",
    );
  };

  const canDeviceBeReturned = (
    deviceId: string,
  ): { canReturn: boolean; reason?: string } => {
    // التحقق من أن الجهاز موجود
    const device = devices.find((d) => d.id === deviceId);
    if (!device) {
      return { canReturn: false, reason: "الجهاز غير موجود في النظام" };
    }

    // التحقق من أن الجهاز ليس في المخزون حالياً
    const inventoryStatuses: DeviceStatus[] = [
      "متاح للبيع",
      "بانتظار إرسال للصيانة",
      "بانتظار استلام في الصيانة",
      "قيد الإصلاح",
      "بانتظار تسليم من الصيانة",
      "بانتظار استلام في المخزن",
      "تالف",
      "معيب",
      "قيد النقل",
    ];

    if (inventoryStatuses.includes(device.status)) {
      return {
        canReturn: false,
        reason: "الجهاز موجود حالياً في المخزون ولم يتم بيعه",
      };
    }

    // التحقق من أن الجهاز تم بيعه
    const isDeviceSold = sales.some((sale) =>
      sale.items.some((item) => item.deviceId === deviceId),
    );

    if (!isDeviceSold) {
      return { canReturn: false, reason: "الجهاز لم يتم بيعه من قبل" };
    }

    // التحقق من أن الجهاز لم يتم إرجاعه سابقاً
    const isAlreadyReturned = isDeviceReturned(deviceId);
    if (isAlreadyReturned) {
      return { canReturn: false, reason: "الجهاز تم إرجاعه سابقاً" };
    }

    return { canReturn: true };
  };

  const addDeviceReturnHistory = (
    history: Omit<DeviceReturnHistory, "deviceId"> & { deviceId: string },
  ) => {
    setDeviceReturnHistory((prev) => [...prev, history]);
  };

  // Device CRUD
  const addDevice = async (device: Omit<Device, "id"> & { id?: string }) => {
    try {
      const { apiClient, handleApiResponse } = await import('@/lib/api-client');
      
      const newDevice = {
        ...device,
        id: device.id || `DEV-${Date.now()}`,
        dateAdded: new Date().toISOString(),
      } as Device;
      
      const response = await apiClient.post("/api/devices", newDevice);
      const createdDevice = await handleApiResponse(response) as Device;

      setDevices((prev) => [...prev, createdDevice]);
      addActivity({
        type: "supply",
        description: `تم توريد جهاز جديد: ${createdDevice.model}`,
      });
    } catch (error) {
      console.error("Failed to add device:", error);
      // Fallback to local storage
      const newDevice = {
        ...device,
        id: device.id || `DEV-${Date.now()}`,
        dateAdded: new Date().toISOString(),
      } as Device;
      if (devices.some((d) => d.id === newDevice.id)) {
        console.error("Device with this ID already exists.");
        return;
      }
      setDevices((prev) => [...prev, newDevice]);
      addActivity({
        type: "supply",
        description: `تم توريد جهاز جديد: ${newDevice.model}`,
      });
    }
  };

  /**  حفظ أمر صيانة (مسوّدة أو فعلي) مع الحفاظ على تسلسل رقم الأمر وعدم تكراره */
  const addMaintenanceOrder = async (
    order: Omit<MaintenanceOrder, "id" | "createdAt"> & {
      id?: number;
      status?: "wip" | "completed" | "draft";
    },
  ) => {
    try {
      // إذا لم يكن هناك رقم أمر، نقوم بإنشاء واحد
      if (!order.orderNumber) {
        const allExisting = [...maintenanceOrders];
        const useId =
          order.id && order.id > 0
            ? order.id
            : Math.max(0, ...allExisting.map((o) => o.id)) + 1;
        order.orderNumber = `MAINT-${useId}`;
      }

      // إرسال الأمر إلى API
      const { apiClient, handleApiResponse } = await import('@/lib/api-client');

      const response = await apiClient.post("/api/maintenance-orders", {
        ...order,
        status: order.status || 'wip',
      }, {
        headers: getAuthHeader()
      });

      // استقبال الأمر الذي تم إنشاؤه من API
      const rawOrder = await handleApiResponse(response);

      // تحويل items من JSON string إلى array إذا لزم الأمر
      const newOrder = {
        ...rawOrder,
        items: rawOrder.items ? (typeof rawOrder.items === 'string' ? JSON.parse(rawOrder.items) : rawOrder.items) : []
      } as MaintenanceOrder;

      // تحديث حالة التطبيق
      setMaintenanceOrders((prev) => [newOrder, ...prev]);

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: `تم إنشاء أمر صيانة ${newOrder.orderNumber}.`,
      });
      
      return newOrder;
    } catch (error) {
      console.error('Failed to add maintenance order:', error);
      addActivity({
        type: "maintenance",
        description: `⚠️ فشل في إنشاء أمر صيانة: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  

  const updateMaintenanceOrder = (updatedOrder: MaintenanceOrder) => {
    setMaintenanceOrders((prev) =>
      prev.map((o) =>
        o.id === updatedOrder.id
          ? {
              ...updatedOrder,
              createdAt: o.createdAt || new Date().toISOString(), // ← الحفاظ على التاريخ الأصلي
            }
          : o,
      ),
    );

    const originalOrder = maintenanceOrders.find(
      (o) => o.id === updatedOrder.id,
    );
    if (originalOrder) {
      const originalDeviceIds = new Set(originalOrder.items.map((i) => i.id));
      const updatedDeviceIds = new Set(updatedOrder.items.map((i) => i.id));

      const removedDeviceIds = [...originalDeviceIds].filter(
        (id) => !updatedDeviceIds.has(id),
      );
      setDevices((prev) =>
        prev.map((device) =>
          removedDeviceIds.includes(device.id)
            ? { ...device, status: "تحتاج صيانة" }
            : device,
        ),
      );
    }

    updatedOrder.items.forEach((item) => {
      updateDeviceStatus(item.id, "قيد الإصلاح");
    });

    addActivity({
      type: "maintenance",
      description: `تم تحديث أمر الصيانة ${updatedOrder.orderNumber}`,
    });
  };

  // فحص العلاقات الشامل لأوامر الصيانة - التحقق من الأوامر اللاحقة فقط
  const checkMaintenanceOrderRelations = (
    orderId: number,
  ): {
    canDelete: boolean;
    reason?: string;
    relatedOperations?: string[];
  } => {
    const orderToDelete = maintenanceOrders.find((o) => o.id === orderId);
    if (!orderToDelete)
      return { canDelete: false, reason: "أمر الصيانة غير موجود" };

    const deviceIdsInOrder = orderToDelete.items.map((item) => item.id);
    const relatedOperations: string[] = [];
    const orderDate = new Date(orderToDelete.date);

    // فحص المبيعات اللاحقة فقط
    const relatedSales = sales.filter((sale) => {
      const saleDate = new Date(sale.date);
      return saleDate > orderDate &&
        sale.items.some((item) => deviceIdsInOrder.includes(item.deviceId));
    });
    if (relatedSales.length > 0) {
      relatedOperations.push(`${relatedSales.length} فاتورة مبيعات لاحقة`);
    }

    // فحص المرتجعات اللاحقة فقط
    const relatedReturns = returns.filter((returnOrder) => {
      const returnDate = new Date(returnOrder.date);
      return returnDate > orderDate &&
        returnOrder.items.some((item) =>
          deviceIdsInOrder.includes(item.deviceId),
        );
    });
    if (relatedReturns.length > 0) {
      relatedOperations.push(`${relatedReturns.length} أمر مرتجع لاحق`);
    }

    // فحص أوامر التقييم اللاحقة فقط
    const relatedEvaluationOrders = evaluationOrders.filter((order) => {
      const evalDate = new Date(order.date);
      return evalDate > orderDate &&
        order.items.some((item) => deviceIdsInOrder.includes(item.deviceId));
    });
    if (relatedEvaluationOrders.length > 0) {
      relatedOperations.push(`${relatedEvaluationOrders.length} أمر تقييم لاحق`);
    }

    // فحص التحويلات المخزنية اللاحقة فقط
    const relatedWarehouseTransfers = warehouseTransfers.filter((transfer) => {
      const transferDate = new Date(transfer.date);
      return transferDate > orderDate &&
        transfer.items.some((item) => deviceIdsInOrder.includes(item.deviceId));
    });
    if (relatedWarehouseTransfers.length > 0) {
      relatedOperations.push(`${relatedWarehouseTransfers.length} تحويل مخزني لاحق`);
    }

    // فحص أوامر التسليم اللاحقة فقط
    const relatedDeliveryOrders = deliveryOrders.filter((order) => {
      const deliveryDate = new Date(order.date);
      return deliveryDate > orderDate &&
        order.items.some((item) => deviceIdsInOrder.includes(item.deviceId));
    });
    if (relatedDeliveryOrders.length > 0) {
      relatedOperations.push(`${relatedDeliveryOrders.length} أمر تسليم لاحق`);
    }

    // فحص أوامر استلام الصيانة اللاحقة فقط
    const relatedMaintenanceReceipts = maintenanceReceiptOrders.filter((order) => {
      const receiptDate = new Date(order.date);
      return receiptDate > orderDate &&
        order.items.some((item) => deviceIdsInOrder.includes(item.id));
    });
    if (relatedMaintenanceReceipts.length > 0) {
      relatedOperations.push(
        `${relatedMaintenanceReceipts.length} أمر استلام صيانة لاحق`,
      );
    }

    // فحص سجلات الصيانة اللاحقة فقط
    const relatedMaintenanceHistory = maintenanceHistory.filter((log) => {
      const logDate = new Date(log.timestamp);
      return logDate > orderDate && deviceIdsInOrder.includes(log.deviceId);
    });
    if (relatedMaintenanceHistory.length > 0) {
      relatedOperations.push(`${relatedMaintenanceHistory.length} سجل صيانة لاحق`);
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: "يوجد عمليات مرتبطة بأجهزة هذا الأمر",
        relatedOperations,
      };
    }

    return { canDelete: true };
  };

  const deleteMaintenanceOrder = async (orderId: number) => {
    try {
      const relationCheck = checkMaintenanceOrderRelations(orderId);
      if (!relationCheck.canDelete) {
        throw new Error(
          `لا يمكن حذف أمر الصيانة: ${relationCheck.reason}${relationCheck.relatedOperations ? "\nالعمليات المرتبطة: " + relationCheck.relatedOperations.join(", ") : ""}`,
        );
      }

      const orderToDelete = maintenanceOrders.find((o) => o.id === orderId);
      if (!orderToDelete) return;

      // حذف الأمر من قاعدة البيانات أولاً
      const response = await apiClient.delete(`/api/maintenance-orders/${orderId}`, undefined, {
        headers: getAuthHeader()
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Delete API response:', response.status, errorText);
        throw new Error(`Failed to delete maintenance order from database: ${response.status} - ${errorText}`);
      }

      // إعادة حالة الأجهزة إلى "بانتظار استلام في الصيانة"
      const deviceIdsToRevert = orderToDelete.items.map((item) => item.id);
      for (const deviceId of deviceIdsToRevert) {
        await updateDeviceStatus(deviceId, "بانتظار استلام في الصيانة");
      }

      // حذف الأمر من الحالة المحلية
      setMaintenanceOrders((prev) => prev.filter((o) => o.id !== orderId));

      addActivity({
        type: "maintenance",
        description: `تم حذف أمر الصيانة ${orderToDelete.orderNumber} وإعادة الأجهزة إلى قائمة الانتظار`,
      });
    } catch (error) {
      console.error('Failed to delete maintenance order:', error);
      addActivity({
        type: "maintenance",
        description: `⚠️ فشل في حذف أمر الصيانة: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  const addDeliveryOrder = async (
    order: Omit<DeliveryOrder, "id" | "createdAt">,
  ) => {
    try {
      const { apiClient, handleApiResponse } = await import('@/lib/api-client');

      const response = await apiClient.post("/api/delivery-orders", order, {
        headers: getAuthHeader()
      });

      const newOrder = await handleApiResponse(response) as DeliveryOrder;
      setDeliveryOrders((prev) => [newOrder, ...prev]);

      // إعادة تحميل الأجهزة وأوامر التسليم لتحديث البيانات
      try {
        const [devicesResponse, deliveryOrdersResponse] = await Promise.all([
          apiClient.get("/api/devices", { headers: getAuthHeader() }),
          apiClient.get("/api/delivery-orders", { headers: getAuthHeader() })
        ]);

        const devicesData = await handleApiResponse(devicesResponse);
        if (Array.isArray(devicesData)) {
          setDevices(devicesData);
        }

        const deliveryOrdersData = await handleApiResponse(deliveryOrdersResponse);
        if (Array.isArray(deliveryOrdersData)) {
          setDeliveryOrders(deliveryOrdersData);
        }
      } catch (error) {
        console.error('Failed to reload data after delivery order creation:', error);
      }

      addActivity({
        type: "maintenance",
        description: `تم إنشاء أمر تسليم صيانة ${newOrder.deliveryOrderNumber}`,
      });

      return newOrder;
    } catch (error) {
      console.error("Failed to create delivery order:", error);
      throw error;
    }
  };

  const updateDeliveryOrder = async (updatedOrder: DeliveryOrder) => {
    try {
      const response = await apiClient.put("/api/delivery-orders", updatedOrder);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update delivery order");
      }

      const order = await response.json();
      setDeliveryOrders((prev) =>
        prev.map((o) => (o.id === order.id ? order : o)),
      );

      addActivity({
        type: "maintenance",
        description: `تم تحديث أمر التسليم ${order.deliveryOrderNumber}`,
      });

      return order;
    } catch (error) {
      console.error("Failed to update delivery order:", error);
      throw error;
    }
  };

  // فحص العلاقات الشامل لأوامر التسليم - التحقق من الأوامر اللاحقة فقط
  const checkDeliveryOrderRelations = (
    orderId: number,
  ): {
    canDelete: boolean;
    reason?: string;
    relatedOperations?: string[];
  } => {
    const orderToDelete = deliveryOrders.find((o) => o.id === orderId);
    if (!orderToDelete)
      return { canDelete: false, reason: "أمر التسليم غير موجود" };

    const deviceIdsInOrder = orderToDelete.items.map((item) => item.deviceId);
    const relatedOperations: string[] = [];
    const orderDate = new Date(orderToDelete.date);

    // فحص المبيعات اللاحقة فقط
    const relatedSales = sales.filter((sale) => {
      const saleDate = new Date(sale.date);
      return saleDate > orderDate &&
        sale.items.some((item) => deviceIdsInOrder.includes(item.deviceId));
    });
    if (relatedSales.length > 0) {
      relatedOperations.push(`${relatedSales.length} فاتورة مبيعات لاحقة`);
    }

    // فحص المرتجعات اللاحقة فقط
    const relatedReturns = returns.filter((returnOrder) => {
      const returnDate = new Date(returnOrder.date);
      return returnDate > orderDate &&
        returnOrder.items.some((item) =>
          deviceIdsInOrder.includes(item.deviceId),
        );
    });
    if (relatedReturns.length > 0) {
      relatedOperations.push(`${relatedReturns.length} أمر مرتجع لاحق`);
    }

    // فحص التحويلات المخزنية اللاحقة فقط
    const relatedTransfers = warehouseTransfers.filter((transfer) => {
      const transferDate = new Date(transfer.date);
      return transferDate > orderDate &&
        transfer.items.some((item) => deviceIdsInOrder.includes(item.deviceId));
    });
    if (relatedTransfers.length > 0) {
      relatedOperations.push(`${relatedTransfers.length} تحويل مخزني لاحق`);
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: "يوجد عمليات مرتبطة بأجهزة هذا الأمر",
        relatedOperations,
      };
    }

    return { canDelete: true };
  };

  const deleteDeliveryOrder = async (orderId: number) => {
    try {
      const relationCheck = checkDeliveryOrderRelations(orderId);
      if (!relationCheck.canDelete) {
        throw new Error(
          `لا يمكن حذف أمر التسليم: ${relationCheck.reason}${relationCheck.relatedOperations ? "\nالعمليات المرتبطة: " + relationCheck.relatedOperations.join(", ") : ""}`,
        );
      }

      const response = await apiClient.delete(`/api/delivery-orders/${orderId}`, undefined, {
        headers: getAuthHeader()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete delivery order");
      }

      const orderToDelete = deliveryOrders.find((o) => o.id === orderId);
      if (!orderToDelete) return;

      // إعادة حالة الأجهزة إلى "قيد الإصلاح" عند حذف أمر التسليم
      orderToDelete.items.forEach(item => {
        const device = devices.find(d => d.id === item.deviceId);
        if (device && device.status === 'بانتظار استلام في المخزن') {
          updateDeviceStatus(item.deviceId, 'قيد الإصلاح');
        }
      });

      setDeliveryOrders((prev) => prev.filter((o) => o.id !== orderId));

      addActivity({
        type: "maintenance",
        description: `تم حذف أمر التسليم ${orderToDelete.deliveryOrderNumber} وإعادة ${orderToDelete.items.length} جهاز إلى حالة "قيد الإصلاح"`,
      });
    } catch (error) {
      console.error("Failed to delete delivery order:", error);
      throw error;
    }
  };

  // وظائف أوامر الاستلام من الصيانة
    const addMaintenanceReceiptOrder = async (
    order: Omit<MaintenanceReceiptOrder, "id" | "createdAt">,
  ) => {
    try {
      // إذا لم يكن هناك رقم للاستلام، نقوم بإنشاء واحد
      if (!order.receiptNumber) {
        const maxId = maintenanceReceiptOrders.reduce(
          (max, o) => (o.id > max ? o.id : max),
          0
        );
        order.receiptNumber = `MREC-${maxId + 1}`;
      }
      
      // إرسال الأمر إلى API
      const { apiClient, handleApiResponse } = await import('@/lib/api-client');

      const response = await apiClient.post("/api/maintenance-receipts", order, {
        headers: getAuthHeader()
      });

      // استقبال الأمر الذي تم إنشاؤه من API
      const rawOrder = await handleApiResponse(response);

      // تحويل items من JSON string إلى array إذا لزم الأمر
      const newOrder = {
        ...rawOrder,
        items: rawOrder.items ? (typeof rawOrder.items === 'string' ? JSON.parse(rawOrder.items) : rawOrder.items) : []
      } as MaintenanceReceiptOrder;

      // تحديث حالة التطبيق
      setMaintenanceReceiptOrders((prev) => [newOrder, ...prev]);

      // إعادة تحميل الأجهزة لتحديث حالتها
      try {
        const devicesResponse = await apiClient.get("/api/devices", {
          headers: getAuthHeader()
        });
        const devicesData = await handleApiResponse(devicesResponse);
        if (Array.isArray(devicesData)) {
          setDevices(devicesData);
        }
      } catch (error) {
        console.error('Failed to reload devices after maintenance receipt creation:', error);
      }

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: `تم إنشاء أمر استلام جديد ${newOrder.receiptNumber} يحتوي على ${newOrder.items.length} جهاز`,
      });
      
      return newOrder;
    } catch (error) {
      console.error('Failed to add maintenance receipt order:', error);
      addActivity({
        type: "maintenance",
        description: `⚠️ فشل في إنشاء أمر استلام صيانة: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

    const updateMaintenanceReceiptOrder = async (
    updatedOrder: MaintenanceReceiptOrder,
  ) => {
    try {
      // إرسال الأمر إلى API
      const response = await apiClient.put("/api/maintenance-receipts", updatedOrder);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update maintenance receipt order');
      }

      // استقبال الأمر المحدّث من API
      const savedOrder = await response.json();

      // تحديث حالة التطبيق
      setMaintenanceReceiptOrders((prev) =>
        prev.map((o) =>
          o.id === savedOrder.id
            ? savedOrder
            : o
        ),
      );

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: `تم تحديث أمر الاستلام ${updatedOrder.receiptNumber}`,
      });
      
      return savedOrder;
    } catch (error) {
      console.error('Failed to update maintenance receipt order:', error);
      addActivity({
        type: "maintenance",
        description: `⚠️ فشل في تحديث أمر استلام صيانة: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  // فحص العلاقات الشامل للأجهزة الفردية في الصيانة
  const checkDeviceRelationsInMaintenance = (
    deviceId: string,
    currentMaintenanceOrderId?: number,
  ) => {
    const relatedOps: string[] = [];

    // فحص المبيعات - أي مبيعات تحتوي على هذا الجهاز
    const deviceInSales = sales.some((sale) =>
      sale.items.some((item) => item.deviceId === deviceId),
    );
    if (deviceInSales) relatedOps.push("مبيعات");

    // فحص المرتجعات - أي مرتجعات تحتوي على هذا الجهاز
    const deviceInReturns = returns.some((returnOrder) =>
      returnOrder.items.some((item) => item.deviceId === deviceId),
    );
    if (deviceInReturns) relatedOps.push("مرتجعات");

    // فحص أوامر التقييم - أي تقييم يحتوي على هذا الجهاز
    const deviceInEvaluations = evaluationOrders.some((evalOrder) =>
      evalOrder.items.some((item) => item.deviceId === deviceId),
    );
    if (deviceInEvaluations) relatedOps.push("تقييم");

    // فحص التحويلات المخزنية - أي تحويل يحتوي على هذا الجهاز
    const deviceInTransfers = warehouseTransfers.some((transfer) =>
      transfer.items.some((item) => item.deviceId === deviceId),
    );
    if (deviceInTransfers) relatedOps.push("تحويلات مخزنية");

    // فحص سجلات الصيانة - أي صيانة لهذا الجهاز
    const deviceInMaintenanceHistory = maintenanceHistory.some(
      (maintenance) => maintenance.deviceId === deviceId,
    );
    if (deviceInMaintenanceHistory) relatedOps.push("صيانة");

    // فحص أوامر الصيانة الأخرى - إذا كان الجهاز موجود في أوامر صيانة أخرى
    const deviceInOtherMaintenanceOrders = maintenanceOrders.some(
      (order) =>
        order.id !== currentMaintenanceOrderId &&
        order.items.some((item) => item.id === deviceId),
    );
    if (deviceInOtherMaintenanceOrders) relatedOps.push("أوامر صيانة أخرى");

    // فحص أوامر التسليم - أي أمر تسليم يحتوي على هذا الجهاز
    const deviceInDeliveryOrders = deliveryOrders.some((order) =>
      order.items.some((item) => item.deviceId === deviceId),
    );
    if (deviceInDeliveryOrders) relatedOps.push("أوامر تسليم");

    // فحص أوامر استلام الصيانة - أي أمر استلام يحتوي على هذا الجهاز
    const deviceInMaintenanceReceipts = maintenanceReceiptOrders.some((order) =>
      order.items.some((item) => item.id === deviceId),
    );
    if (deviceInMaintenanceReceipts) relatedOps.push("أوامر استلام صيانة");

    // فحص أوامر التوريد - إذا كان الجهاز موجود في أوامر توريد
    const deviceInSupplyOrders = supplyOrders.some((order) =>
      order.items.some((item) => item.imei === deviceId),
    );
    if (deviceInSupplyOrders) relatedOps.push("أوامر توريد");

    // فحص عمليات الجرد - أي جرد يحتوي على هذا الجهاز
    const deviceInStocktakes = stocktakes.some(
      (stocktake) =>
        stocktake.items &&
        stocktake.items.some((item) => item.deviceId === deviceId),
    );
    if (deviceInStocktakes) relatedOps.push("عمليات جرد");

    return {
      hasRelations: relatedOps.length > 0,
      relatedOperations: relatedOps,
    };
  };

    const deleteMaintenanceReceiptOrder = async (orderId: number) => {
    try {
      const orderToDelete = maintenanceReceiptOrders.find((o) => o.id === orderId);
      if (!orderToDelete) return;

      // إرسال طلب الحذف إلى API
      const response = await apiClient.delete("/api/maintenance-receipts", { id: orderId });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete maintenance receipt order');
      }

      // إعادة حالة الأجهزة إلى "بانتظار استلام في المخزن" عند حذف أمر الاستلام
      orderToDelete.items.forEach(device => {
        const currentDevice = devices.find(d => d.id === device.id);
        if (currentDevice && currentDevice.status === 'متاح للبيع') {
          updateDeviceStatus(device.id, 'بانتظار استلام في المخزن');
        }
      });

      // تحديث حالة التطبيق
      setMaintenanceReceiptOrders((prev) => prev.filter((o) => o.id !== orderId));

      // إضافة نشاط
      addActivity({
        type: "maintenance",
        description: `تم حذف أمر الاستلام ${orderToDelete.receiptNumber} وإعادة ${orderToDelete.items.length} جهاز إلى حالة "بانتظار استلام في المخزن"`,
      });
    } catch (error) {
      console.error('Failed to delete maintenance receipt order:', error);
      addActivity({
        type: "maintenance",
        description: `⚠️ فشل في حذف أمر استلام صيانة: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  // وظائف الجرد
  const addStocktake = async (
    stocktakeData: Omit<
      StocktakeV1,
      "id" | "operationNumber" | "createdAt" | "lastModifiedAt"
    >,
  ) => {
    try {
      // إعداد البيانات الأساسية
      const operationNumber = `ST-${Date.now()}`;
      const now = new Date().toISOString();

      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إنشاء /api/stocktakes endpoint
      
      const newId = Math.max(0, ...stocktakes.map((s) => s.id)) + 1;
      const newStocktake: StocktakeV1 = {
        ...stocktakeData,
        id: newId,
        operationNumber,
        createdAt: now,
        lastModifiedAt: now,
        items: [], // Initialize items array
        discrepancies: [], // Initialize discrepancies array
        totalExpected: 0,
        totalScanned: 0,
        totalMatching: 0,
        totalDiscrepancies: 0,
        status: 'في التقدم' as StocktakeStatus,
      };

      setStocktakes((prev) => [newStocktake, ...prev]);

      addActivity({
        type: "supply",
        description: `تم إنشاء عملية جرد جديدة: ${operationNumber}`,
      });
      
      return newStocktake;
    } catch (error) {
      console.error('Failed to add stocktake:', error);
      addActivity({
        type: "supply",
        description: `⚠️ فشل في إنشاء عملية جرد: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  

  const updateStocktake = async (stocktake: StocktakeV1) => {
    try {
      // تحديث الوقت المعدل
      const updatedStocktake = {
        ...stocktake,
        lastModifiedAt: new Date().toISOString(),
      };

      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إرسال إلى /api/stocktakes

      setStocktakes((prev) =>
        prev.map((s) => (s.id === updatedStocktake.id ? updatedStocktake : s)),
      );

      addActivity({
        type: "supply",
        description: `تم تحديث عملية الجرد: ${updatedStocktake.operationNumber}`,
      });
      
      return updatedStocktake;
    } catch (error) {
      console.error('Failed to update stocktake:', error);
      addActivity({
        type: "supply",
        description: `⚠️ فشل في تحديث عملية الجرد: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  const deleteStocktake = async (stocktakeId: number) => {
    try {
      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إرسال إلى /api/stocktakes

      setStocktakes((prev) => prev.filter((s) => s.id !== stocktakeId));

      addActivity({
        type: "supply",
        description: `تم حذف عملية الجرد رقم ${stocktakeId}`,
      });
    } catch (error) {
      console.error('Failed to delete stocktake:', error);
      addActivity({
        type: "supply",
        description: `⚠️ فشل في حذف عملية الجرد: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  

  const addStocktakeItem = (stocktakeId: number, item: StocktakeItemV1) => {
    setStocktakes((prev) =>
      prev.map((s) =>
        s.id === stocktakeId
          ? {
              ...s,
              items: [...s.items, item],
              lastModifiedAt: new Date().toISOString(),
            }
          : s,
      ),
    );
  };

  const updateStocktakeItem = (
    stocktakeId: number,
    deviceId: string,
    updates: Partial<StocktakeItemV1>,
  ) => {
    setStocktakes((prev) =>
      prev.map((s) =>
        s.id === stocktakeId
          ? {
              ...s,
              items: s.items.map((item) =>
                item.deviceId === deviceId ? { ...item, ...updates } : item,
              ),
              lastModifiedAt: new Date().toISOString(),
            }
          : s,
      ),
    );
  };

  const addStocktakeDiscrepancy = (
    stocktakeId: number,
    discrepancy: Omit<
      StocktakeDiscrepancy,
      "resolved" | "resolvedBy" | "resolvedAt"
    >,
  ) => {
    setStocktakes((prev) =>
      prev.map((s) =>
        s.id === stocktakeId
          ? {
              ...s,
              discrepancies: [
                ...s.discrepancies,
                { ...discrepancy, resolved: false },
              ],
              lastModifiedAt: new Date().toISOString(),
            }
          : s,
      ),
    );
  };

  const resolveStocktakeDiscrepancy = (
    stocktakeId: number,
    discrepancyIndex: number,
    resolutionNotes: string,
    resolvedBy: number,
  ) => {
    setStocktakes((prev) =>
      prev.map((s) =>
        s.id === stocktakeId
          ? {
              ...s,
              discrepancies: s.discrepancies.map((d, index) =>
                index === discrepancyIndex
                  ? {
                      ...d,
                      resolved: true,
                      resolutionNotes,
                      resolvedBy,
                      resolvedAt: new Date().toISOString(),
                    }
                  : d,
              ),
              lastModifiedAt: new Date().toISOString(),
            }
          : s,
      ),
    );
  };

  const changeStocktakeStatus = (
    stocktakeId: number,
    status: StocktakeStatus,
  ) => {
    setStocktakes((prev) =>
      prev.map((s) =>
        s.id === stocktakeId
          ? {
              ...s,
              status,
              ...(status === "جاري" && !s.startedAt
                ? { startedAt: new Date().toISOString() }
                : {}),
              ...(status === "مكتمل"
                ? { completedAt: new Date().toISOString() }
                : {}),
              lastModifiedAt: new Date().toISOString(),
            }
          : s,
      ),
    );
  };

  const reviewStocktake = (
    stocktakeId: number,
    reviewedBy: number,
    reviewedByName: string,
    reviewNotes: string,
    approved: boolean,
  ) => {
    setStocktakes((prev) =>
      prev.map((s) =>
        s.id === stocktakeId
          ? {
              ...s,
              reviewedBy,
              reviewedByName,
              reviewedAt: new Date().toISOString(),
              reviewNotes,
              approved,
              lastModifiedAt: new Date().toISOString(),
            }
          : s,
      ),
    );
  };

  // Acceptance Orders
  const addAcceptanceOrder = (
    order: Omit<AcceptanceOrder, "id" | "acceptanceId">,
  ) => {
    const maxId = acceptanceOrders.reduce(
      (max, o) => (o.id > max ? o.id : max),
      0,
    );
    const newId = maxId + 1;
    const newOrder: AcceptanceOrder = {
      ...order,
      id: newId,
      acceptanceId: `ACC-${newId}`,
      date: new Date().toISOString(),
    };
    setAcceptanceOrders((prev) =>
      [newOrder, ...prev].sort((a, b) => b.id - a.id),
    );

    newOrder.items.forEach((item) => {
      const newDevice: Device = {
        id: item.deviceId,
        model: item.model,
        status: "متاح للبيع",
        storage: item.storage,
        price: item.price,
        condition: item.condition,
        warehouseId: newOrder.warehouseId,
        dateAdded: new Date().toISOString(),
      };
      setDevices((prev) => [...prev, newDevice]);
    });

    const warehouse = warehouses.find((w) => w.id === newOrder.warehouseId);
    addActivity({
      type: "supply",
      description: `تم قبول ${newOrder.items.length} أجهزة في المخزن ${warehouse?.name || "غير معروف"}`,
    });
  };

  const updateAcceptanceOrder = (updatedOrder: AcceptanceOrder) => {
    setAcceptanceOrders((prev) =>
      prev.map((o) => (o.id === updatedOrder.id ? updatedOrder : o)),
    );
    addActivity({
      type: "supply",
      description: `تم تحديث أمر القبول ${updatedOrder.acceptanceId}`,
    });
  };

  const deleteAcceptanceOrder = (orderId: number) => {
    const orderToDelete = acceptanceOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return;

    // Remove associated devices from inventory
    const deviceIdsToRemove = orderToDelete.items.map((item) => item.deviceId);
    setDevices((prevDevices) =>
      prevDevices.filter((device) => !deviceIdsToRemove.includes(device.id)),
    );

    setAcceptanceOrders((prev) => prev.filter((o) => o.id !== orderId));
    addActivity({
      type: "supply",
      description: `تم حذف أمر القبول ${orderToDelete.acceptanceId}`,
    });
  };

  const updateDevice = (updatedDevice: Device) => {
    setDevices((prev) =>
      prev.map((d) => (d.id === updatedDevice.id ? updatedDevice : d)),
    );
  };

  const deleteDevice = (deviceId: string) => {
    setDevices((prev) => prev.filter((d) => d.id !== deviceId));
  };

  const getDevicesByIds = (ids: string[]) => {
    return devices.filter((d) => ids.includes(d.id));
  };

  // Contact CRUD
  const addContact = async (
    contact: Omit<Contact, "id">,
    type: "client" | "supplier",
  ) => {
    try {
      const { apiClient, handleApiResponse } = await import('@/lib/api-client');

      const endpoint = type === "client" ? "/api/clients" : "/api/suppliers";
      const response = await apiClient.post(endpoint, contact);

      const newContact = await handleApiResponse(response) as Contact;
      if (type === "client") {
        setClients((prev) => [newContact, ...prev].sort((a, b) => b.id - a.id));
      } else {
        setSuppliers((prev) =>
          [newContact, ...prev].sort((a, b) => b.id - a.id),
        );
      }

      addActivity({
        type: type === "client" ? "sale" : "supply",
        description: `تم إضافة ${type === "client" ? "عميل" : "مورد"} جديد: ${newContact.name}`,
      });
    } catch (error) {
      console.error(`Failed to add ${type}:`, error);
      throw error;
    }
  };

  const updateContact = async (
    updatedContact: Contact,
    type: "client" | "supplier",
  ) => {
    try {
      const endpoint = type === "client" ? "/api/clients" : "/api/suppliers";
      const response = await apiClient.put(endpoint, updatedContact);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update ${type}`);
      }

      const contact = await response.json();
      if (type === "client") {
        setClients((prev) =>
          prev.map((c) => (c.id === contact.id ? contact : c)),
        );
      } else {
        setSuppliers((prev) =>
          prev.map((s) => (s.id === contact.id ? contact : s)),
        );
      }

      addActivity({
        type: type === "client" ? "sale" : "supply",
        description: `تم تحديث ${type === "client" ? "عميل" : "مورد"}: ${contact.name}`,
      });
    } catch (error) {
      console.error(`Failed to update ${type}:`, error);
      throw error;
    }
  };

  // فحص العلاقات الشامل للعملاء
  const checkClientRelations = (
    clientId: number,
  ): {
    canDelete: boolean;
    reason?: string;
    relatedOperations?: string[];
  } => {
    const clientToDelete = clients.find((c) => c.id === clientId);
    if (!clientToDelete)
      return { canDelete: false, reason: "العميل غير موجود" };

    const relatedOperations: string[] = [];

    // فحص المبيعات - أي مبيعات لهذا العميل
    const relatedSales = sales.filter(
      (sale) => sale.clientName === clientToDelete.name,
    );
    if (relatedSales.length > 0) {
      relatedOperations.push(`${relatedSales.length} فاتورة مبيعات`);
    }

    // فحص المرتجعات - أي مرتجعات من هذا العميل
    const relatedReturns = returns.filter(
      (returnOrder) => returnOrder.clientName === clientToDelete.name,
    );
    if (relatedReturns.length > 0) {
      relatedOperations.push(`${relatedReturns.length} أمر مرتجع`);
    }

    // ملاحظة: أوامر التسليم لا ترتبط مباشرة بالعملاء في النظام الحالي

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: "يوجد عمليات مرتبطة بهذا العميل",
        relatedOperations,
      };
    }

    return { canDelete: true };
  };

  // فحص العلاقات الشامل للموردين
  const checkSupplierRelations = (
    supplierId: number,
  ): {
    canDelete: boolean;
    reason?: string;
    relatedOperations?: string[];
  } => {
    const supplierToDelete = suppliers.find((s) => s.id === supplierId);
    if (!supplierToDelete)
      return { canDelete: false, reason: "المورد غير موجود" };

    const relatedOperations: string[] = [];

    // فحص أوامر التوريد - أي أوامر توريد من هذا المورد
    const relatedSupplyOrders = supplyOrders.filter(
      (order) => order.supplierId === supplierId,
    );
    if (relatedSupplyOrders.length > 0) {
      relatedOperations.push(`${relatedSupplyOrders.length} أمر توريد`);
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: "يوجد عمليات مرتبطة بهذا المورد",
        relatedOperations,
      };
    }

    return { canDelete: true };
  };

  const deleteContact = async (
    contactId: number,
    type: "client" | "supplier",
  ) => {
    try {
      // فحص العلاقات قبل الحذف
      if (type === "client") {
        const relationCheck = checkClientRelations(contactId);
        if (!relationCheck.canDelete) {
          throw new Error(
            `لا يمكن حذف العميل: ${relationCheck.reason}${relationCheck.relatedOperations ? "\nالعمليات المرتبطة: " + relationCheck.relatedOperations.join(", ") : ""}`,
          );
        }
      } else {
        const relationCheck = checkSupplierRelations(contactId);
        if (!relationCheck.canDelete) {
          throw new Error(
            `لا يمكن حذف المورد: ${relationCheck.reason}${relationCheck.relatedOperations ? "\nالعمليات المرتبطة: " + relationCheck.relatedOperations.join(", ") : ""}`,
          );
        }
      }

      const endpoint = type === "client" ? "/api/clients" : "/api/suppliers";
      const response = await apiClient.delete(endpoint, { id: contactId });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to delete ${type}`);
      }

      if (type === "client") {
        setClients((prev) => prev.filter((c) => c.id !== contactId));
      } else {
        setSuppliers((prev) => prev.filter((s) => s.id !== contactId));
      }

      addActivity({
        type: type === "client" ? "sale" : "supply",
        description: `تم حذف ${type === "client" ? "عميل" : "مورد"}`,
      });
    } catch (error) {
      console.error(`Failed to delete ${type}:`, error);
      throw error;
    }
  };

  // Warehouse CRUD
  const addWarehouse = async (warehouse: Omit<Warehouse, "id">) => {
    try {
      const response = await apiClient.post("/api/warehouses", warehouse);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create warehouse");
      }

      const newWarehouse = await response.json();
      setWarehouses((prev) =>
        [newWarehouse, ...prev].sort((a, b) => b.id - a.id),
      );

      addActivity({
        type: "supply",
        description: `تم إضافة مخزن جديد: ${newWarehouse.name}`,
      });
    } catch (error) {
      console.error("Failed to add warehouse:", error);
      throw error;
    }
  };

  const updateWarehouse = async (updatedWarehouse: Warehouse) => {
    try {
      const response = await apiClient.put("/api/warehouses", updatedWarehouse);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update warehouse");
      }

      const warehouse = await response.json();
      setWarehouses((prev) =>
        prev.map((w) => (w.id === warehouse.id ? warehouse : w)),
      );

      addActivity({
        type: "supply",
        description: `تم تحديث المخزن: ${warehouse.name}`,
      });
    } catch (error) {
      console.error("Failed to update warehouse:", error);
      throw error;
    }
  };

  // فحص العلاقات الشامل للمخازن
  const checkWarehouseRelations = (
    warehouseId: number,
  ): {
    canDelete: boolean;
    reason?: string;
    relatedOperations?: string[];
  } => {
    const warehouseToDelete = warehouses.find((w) => w.id === warehouseId);
    if (!warehouseToDelete)
      return { canDelete: false, reason: "المخزن غير موجود" };

    const relatedOperations: string[] = [];

    // فحص المبيعات - أي مبيعات من هذا المخزن
    const relatedSales = sales.filter(
      (sale) => sale.warehouseName === warehouseToDelete.name,
    );
    if (relatedSales.length > 0) {
      relatedOperations.push(`${relatedSales.length} فاتورة مبيعات`);
    }

    // فحص أوامر التوريد - أي أوامر توريد لهذا المخزن
    const relatedSupplyOrders = supplyOrders.filter(
      (order) => order.warehouseId === warehouseId,
    );
    if (relatedSupplyOrders.length > 0) {
      relatedOperations.push(`${relatedSupplyOrders.length} أمر توريد`);
    }

    // فحص التحويلات المخزنية - أي تحويلات من أو إلى هذا المخزن
    const relatedTransfers = warehouseTransfers.filter(
      (transfer) =>
        transfer.fromWarehouseId === warehouseId ||
        transfer.toWarehouseId === warehouseId,
    );
    if (relatedTransfers.length > 0) {
      relatedOperations.push(`${relatedTransfers.length} تحويل مخزني`);
    }

    // فحص أوامر التسليم - أي أوامر تسليم من هذا المخزن
    const relatedDeliveries = deliveryOrders.filter(
      (order) => order.warehouseId === warehouseId,
    );
    if (relatedDeliveries.length > 0) {
      relatedOperations.push(`${relatedDeliveries.length} أمر تسليم`);
    }

    // فحص الأجهزة - أي أجهزة موجودة في هذا المخزن
    const devicesInWarehouse = devices.filter(
      (device) => device.warehouseId === warehouseId,
    );
    if (devicesInWarehouse.length > 0) {
      relatedOperations.push(`${devicesInWarehouse.length} جهاز`);
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: "يوجد عمليات أو أجهزة مرتبطة بهذا المخزن",
        relatedOperations,
      };
    }

    return { canDelete: true };
  };

  const deleteWarehouse = async (warehouseId: number) => {
    try {
      // فحص العلاقات قبل الحذف
      const relationCheck = checkWarehouseRelations(warehouseId);
      if (!relationCheck.canDelete) {
        throw new Error(
          `لا يمكن حذف المخزن: ${relationCheck.reason}${relationCheck.relatedOperations ? "\nالعمليات المرتبطة: " + relationCheck.relatedOperations.join(", ") : ""}`,
        );
      }

      const response = await apiClient.delete("/api/warehouses", { id: warehouseId });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete warehouse");
      }

      const warehouseToDelete = warehouses.find((w) => w.id === warehouseId);
      setWarehouses((prev) => prev.filter((w) => w.id !== warehouseId));

      addActivity({
        type: "supply",
        description: `تم حذف المخزن: ${warehouseToDelete?.name || "غير معروف"}`,
      });
    } catch (error) {
      console.error("Failed to delete warehouse:", error);
      throw error;
    }
  };

  // Manufacturer & Model CRUD
  const addManufacturer = (
    manufacturer: Omit<Manufacturer, "id">,
  ): Manufacturer => {
    const newManufacturer = { ...manufacturer, id: Date.now() };
    setManufacturers((prev) => [newManufacturer, ...prev]);
    return newManufacturer;
  };

  const addDeviceModel = async (model: Omit<DeviceModel, "id">) => {
    try {
      const response = await apiClient.post("/api/device-models", model);

      if (!response.ok) {
        throw new Error("Failed to create device model");
      }

      const newModel = await response.json();
      setDeviceModels((prev) => [newModel, ...prev]);
    } catch (error) {
      console.error("Failed to add device model:", error);
      throw error;
    }
  };

  // Sales and Returns
  // Sales and Returns - Updated to use API
  const addSale = async (sale: Omit<Sale, "id" | "soNumber" | "createdAt">) => {
    try {
      const response = await apiClient.post("/api/sales", sale);

      if (!response.ok) {
        throw new Error("Failed to create sale");
      }

      const newSale = await response.json();
      setSales((prev) => [newSale, ...prev].sort((a, b) => b.id - a.id));

      // Update device statuses
      newSale.items.forEach((item: any) => {
        updateDeviceStatus(item.deviceId, "مباع");
      });

      addActivity({
        type: "sale",
        description: `تم بيع ${newSale.items.length} أجهزة للعميل ${newSale.clientName}`,
      });
    } catch (error) {
      console.error("Failed to add sale:", error);
      throw error;
    }
  };

    const updateSale = async (updatedSale: Sale) => {
    try {
      const response = await apiClient.put(`/api/sales`, updatedSale);

      if (!response.ok) {
        throw new Error("Failed to update sale");
      }

      const updatedSaleResult = await response.json();

      const originalSale = sales.find((s) => s.id === updatedSale.id);
      if (!originalSale) return;

      // Devices removed from the sale should be set back to 'متاح للبيع'
      originalSale.items.forEach((item) => {
        if (
          !updatedSale.items.some((newItem) => newItem.deviceId === item.deviceId)
        ) {
          updateDeviceStatus(item.deviceId, "متاح للبيع");
        }
      });

      // Devices added or kept in the sale should be set to 'مباع'
      updatedSale.items.forEach((item) => {
        updateDeviceStatus(item.deviceId, "مباع");
      });

      setSales((prev) =>
        prev.map((s) => (s.id === updatedSale.id ? updatedSaleResult : s)),
      );
      addActivity({
        type: "sale",
        description: `تم تحديث الفاتورة ${updatedSale.soNumber}`,
      });
      
      return updatedSaleResult;
    } catch (error) {
      console.error("Failed to update sale:", error);
      throw error;
    }
  };

    const deleteSale = async (saleId: number) => {
    try {
      // التحقق من العلاقات أولاً
      const relationCheck = checkSaleRelations(saleId);
      if (!relationCheck.canDelete) {
        throw new Error(
          `لا يمكن حذف فاتورة المبيعات: ${relationCheck.reason}${relationCheck.relatedOperations ? "\nالعمليات المرتبطة: " + relationCheck.relatedOperations.join(", ") : ""}`,
        );
      }

      const saleToDelete = sales.find((s) => s.id === saleId);
      if (!saleToDelete) return;

      const response = await apiClient.delete(`/api/sales`, { id: saleId });

      if (!response.ok) {
        throw new Error("Failed to delete sale");
      }

      // Revert devices to 'متاح للبيع' status
      saleToDelete.items.forEach((item) => {
        updateDeviceStatus(item.deviceId, "متاح للبيع");
      });

      setSales((prev) => prev.filter((s) => s.id !== saleId));
      addActivity({
        type: "sale",
        description: `تم حذف الفاتورة ${saleToDelete.soNumber}`,
      });
    } catch (error) {
      console.error("Failed to delete sale:", error);
      throw error;
    }
  };

  const addReturn = async (
    returnVal: Omit<Return, "id" | "roNumber" | "status">,
  ) => {
    // التحقق من صحة جميع الأجهزة قبل إنشاء المرتجع
    for (const item of returnVal.items) {
      const validation = canDeviceBeReturned(item.deviceId);
      if (!validation.canReturn) {
        throw new Error(
          `لا يمكن إرجاع الجهاز ${item.deviceId}: ${validation.reason}`,
        );
      }

      // التحقق من الجهاز البديل في حالة الاستبدال
      if (item.isReplacement && item.replacementDeviceId) {
        const replacementDevice = devices.find(
          (d) => d.id === item.replacementDeviceId,
        );
        if (!replacementDevice) {
          throw new Error(
            `الجهاز البديل ${item.replacementDeviceId} غير موجود`,
          );
        }
        if (replacementDevice.status !== "متاح للبيع") {
          throw new Error(
            `الجهاز البديل ${item.replacementDeviceId} غير متاح للبيع`,
          );
        }
      }
    }

    try {
      const returnData = {
        ...returnVal,
        processedBy: currentUser?.name || "غير محدد",
        processedDate: new Date().toISOString(),
      };

      const response = await apiClient.post("/api/returns", returnData);

      if (!response.ok) {
        throw new Error("Failed to create return");
      }

      const newReturn = await response.json();
      setReturns((prev) => [newReturn, ...prev].sort((a, b) => b.id - a.id));

      const originalSale = sales.find((s) => s.id === newReturn.saleId);

      newReturn.items.forEach((item: any) => {
        // إضافة الجهاز للمخزون حسب سبب الإرجاع
        let newStatus: DeviceStatus;
        if (item.returnReason === "رغبة العميل") {
          newStatus = "متاح للبيع";
        } else if (item.returnReason === "خلل مصنعي") {
          newStatus = "بانتظار إرسال للصيانة";
        } else {
          newStatus = "بانتظار إرسال للصيانة"; // افتراضي للأسباب الأخرى
        }

        updateDeviceStatus(item.deviceId, newStatus);

        // إضافة سجل للجهاز المرتجع
        addDeviceReturnHistory({
          deviceId: item.deviceId,
          returnOrderId: newReturn.id,
          returnDate: newReturn.date,
          returnReason: item.returnReason,
          isReplacement: item.isReplacement || false,
          replacementDeviceId: item.replacementDeviceId,
          originalDeviceId: item.originalDeviceId,
          status: "returned",
        });

        // معالجة الاستبدال
        if (item.isReplacement && item.replacementDeviceId && originalSale) {
          const replacementDevice = devices.find(
            (d) => d.id === item.replacementDeviceId,
          );
          if (replacementDevice) {
            // تحديث معلومات الاستبدال للجهاز الأصلي
            setDevices((prev) =>
              prev.map((d) =>
                d.id === item.deviceId
                  ? {
                      ...d,
                      replacementInfo: {
                        isReplacement: false,
                        replacedWithId: item.replacementDeviceId,
                        returnOrderId: newReturn.id,
                        replacementDate: new Date().toISOString(),
                        replacementReason: item.returnReason,
                      },
                    }
                  : d,
              ),
            );

            // تحديث معلومات الاستبدال للجهاز البديل
            setDevices((prev) =>
              prev.map((d) =>
                d.id === item.replacementDeviceId
                  ? {
                      ...d,
                      status: "مباع",
                      replacementInfo: {
                        isReplacement: true,
                        originalDeviceId: item.deviceId,
                        returnOrderId: newReturn.id,
                        replacementDate: new Date().toISOString(),
                        replacementReason: item.returnReason,
                      },
                    }
                  : d,
              ),
            );

            const newSaleItem: SaleItem = {
              deviceId: replacementDevice.id,
              model: replacementDevice.model,
              price: replacementDevice.price,
              condition: replacementDevice.condition,
            };

            const newSaleForReplacement: Omit<
              Sale,
              "id" | "soNumber" | "createdAt"
            > = {
              opNumber: `OP-RET-${newReturn.id}`,
              date: new Date().toISOString(),
              clientName: newReturn.clientName,
              warehouseName: newReturn.warehouseName,
              employeeName: currentUser?.name || "غير محدد",
              items: [newSaleItem],
              notes: `استبدال للجهاز ${item.deviceId} من المرتجع رقم ${newReturn.roNumber}`,
              warrantyPeriod: originalSale.warrantyPeriod, // نفس مدة الضمان
            };

            addSale(newSaleForReplacement);
          }
        }
      });

      addActivity({
        type: "return",
        description: `تم إنشاء أمر مرتجع ${newReturn.roNumber}`,
      });
    } catch (error) {
      console.error("Failed to add return:", error);
      throw error;
    }
  };

  const updateReturn = async (updatedReturn: Return) => {
    try {
      const response = await apiClient.put("/api/returns", updatedReturn);

      if (!response.ok) {
        throw new Error("Failed to update return");
      }

      const returnRecord = await response.json();
      setReturns((prev) =>
        prev.map((r) => (r.id === returnRecord.id ? returnRecord : r)),
      );

      addActivity({
        type: "return",
        description: `تم تحديث أمر المرتجع ${returnRecord.roNumber}`,
      });
    } catch (error) {
      console.error("Failed to update return:", error);
      throw error;
    }
  };

  const deleteReturn = async (returnId: number) => {
    try {
      const returnToDelete = returns.find((r) => r.id === returnId);
      if (!returnToDelete) return;

      const response = await apiClient.delete("/api/returns", { id: returnId });

      if (!response.ok) {
        throw new Error("Failed to delete return");
      }

      // إرجاع حالات الأجهزة وتحديث سجل المرتجعات
      returnToDelete.items.forEach((item: any) => {
        updateDeviceStatus(item.deviceId, "مباع");

        // تحديث سجل المرتجعات - تغيير الحالة إلى 'resold' بدلاً من حذف السجل
        setDeviceReturnHistory((prev) =>
          prev.map((history) =>
            history.deviceId === item.deviceId &&
            history.returnOrderId === returnId
              ? { ...history, status: "resold" }
              : history,
          ),
        );

        if (item.replacementDeviceId) {
          updateDeviceStatus(item.replacementDeviceId, "متاح للبيع");
        }
      });

      setReturns((prev) => prev.filter((r) => r.id !== returnId));
      addActivity({
        type: "return",
        description: `تم حذف أمر المرتجع ${returnToDelete.roNumber}`,
      });
    } catch (error) {
      console.error("Failed to delete return:", error);
      throw error;
    }
  };

  // Supply Orders
  // Supply Orders - Updated to use API with batch processing support
  const addSupplyOrder = async (
    order: Omit<SupplyOrder, "id" | "createdAt">,
  ) => {
    // إذا كان عدد الأجهزة كبير (أكثر من 100)، استخدم API الدفعات
    const itemCount = order.items?.length || 0;
    const shouldUseBatchAPI = itemCount >= 100;
    const apiEndpoint = shouldUseBatchAPI ? "/api/supply-batch" : "/api/supply";

    try {
      console.log('🔄 Sending supply order to API:', order);
      console.log(`Using ${shouldUseBatchAPI ? 'batch' : 'regular'} API for ${itemCount} devices`);

      const response = await apiClient.post(apiEndpoint, order);
      console.log('📡 API Response status:', response.status);
      console.log('📡 API Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        let errorText;
        try {
          errorText = await response.text();
          console.error('❌ API Error response:', errorText);
        } catch (textError) {
          console.error('❌ Failed to read error response:', textError);
          errorText = 'Unknown error';
        }

        // محاولة تحليل رسالة الخطأ
        let errorMessage = `Failed to create supply order: ${response.status}`;
        try {
          const errorJson = JSON.parse(errorText);
          if (errorJson.error) {
            errorMessage += ` - ${errorJson.error}`;
          }
          if (errorJson.details) {
            errorMessage += ` (${errorJson.details})`;
          }
        } catch (parseError) {
          errorMessage += ` - ${errorText}`;
        }

        throw new Error(errorMessage);
      }

      const responseData = await response.json();
      console.log('✅ Successfully created order:', responseData);

      // التعامل مع استجابة API الدفعات المختلفة
      let newOrder;
      if (responseData.order) {
        // استجابة من API الدفعات
        newOrder = responseData.order;
        console.log('📊 Batch processing results:', responseData.processedDevices);
      } else {
        // استجابة من API العادي
        newOrder = responseData;
      }

      // تحويل items من JSON string إلى array إذا لزم الأمر
      if (typeof newOrder.items === 'string') {
        newOrder.items = JSON.parse(newOrder.items);
      }

      setSupplyOrders((prev) =>
        [newOrder, ...prev].sort((a, b) => b.id - a.id),
      );

      // Add devices to inventory
      if (Array.isArray(newOrder.items)) {
        newOrder.items.forEach((item: any) => {
          const deviceExists = devices.some((d) => d.id === item.imei);
          if (!deviceExists) {
            const newDevice: Device = {
              id: item.imei,
              model: `${item.manufacturer} ${item.model}`,
              status: "متاح للبيع",
              storage: "N/A",
              price: 0,
              condition: item.condition,
              warehouseId: newOrder.warehouseId,
              supplierId: newOrder.supplierId,
              dateAdded: new Date().toISOString(),
            };
            setDevices((prev) => [...prev, newDevice]);
          }
        });
      }

      const supplier = suppliers.find((s) => s.id === newOrder.supplierId);
      const itemsCount = Array.isArray(newOrder.items) ? newOrder.items.length : 0;
      addActivity({
        type: "supply",
        description: `تم استلام ${itemsCount} أجهزة من ${supplier?.name || "مورد"}`,
      });
    } catch (error) {
      console.error("Failed to add supply order:", error);
      console.error('Order data that failed:', {
        itemCount: order.items?.length || 0,
        supplierId: order.supplierId,
        warehouseId: order.warehouseId,
        apiEndpoint: apiEndpoint
      });

      // إعادة رمي الخطأ مع معلومات إضافية
      if (error instanceof Error) {
        throw new Error(`Supply order creation failed: ${error.message}`);
      } else {
        throw new Error(`Supply order creation failed: ${String(error)}`);
      }
    }
  };

  const updateSupplyOrder = async (updatedOrder: SupplyOrder) => {
    try {
      const response = await apiClient.put("/api/supply", updatedOrder);

      if (!response.ok) {
        throw new Error("Failed to update supply order");
      }

      const result = await response.json();
      
      // تحويل items من JSON string إلى array إذا لزم الأمر
      if (typeof result.items === 'string') {
        result.items = JSON.parse(result.items);
      }

      const originalOrder = supplyOrders.find((o) => o.id === updatedOrder.id);
      if (!originalOrder) return;

      // التأكد من أن originalOrder.items هو array
      const originalItems = Array.isArray(originalOrder.items) 
        ? originalOrder.items 
        : (typeof originalOrder.items === 'string' 
          ? JSON.parse(originalOrder.items) 
          : []);

      const originalImeis = new Set(
        originalItems.map((item) => item.imei),
      );
      const updatedImeis = new Set(result.items.map((item) => item.imei));

      // IMEIs to be removed from inventory
      const imeisToRemove = originalItems
        .filter((item) => !updatedImeis.has(item.imei))
        .map((item) => item.imei);

      // Items to be added to inventory
      const itemsToAdd = result.items.filter(
        (item) => !originalImeis.has(item.imei),
      );

      // Remove devices from inventory
      if (imeisToRemove.length > 0) {
        setDevices((prevDevices) =>
          prevDevices.filter((device) => !imeisToRemove.includes(device.id)),
        );
      }

      // Add new devices to inventory
      if (itemsToAdd.length > 0) {
        const newDevices: Device[] = itemsToAdd.map((item) => ({
          id: item.imei,
          model: `${item.manufacturer} ${item.model}`,
          status: "متاح للبيع",
          storage: "N/A",
          price: 0,
          condition: item.condition,
          warehouseId: updatedOrder.warehouseId,
          supplierId: updatedOrder.supplierId,
          dateAdded: new Date().toISOString(),
        }));
        setDevices((prevDevices) => [...prevDevices, ...newDevices]);
      }

      // Update the order itself
      setSupplyOrders((prev) =>
        prev.map((o) => (o.id === updatedOrder.id ? updatedOrder : o)),
      );

      addActivity({
        type: "supply",
        description: `تم تحديث أمر التوريد ${updatedOrder.supplyOrderId}`,
      });
    } catch (error) {
      console.error("Failed to update supply order:", error);
      throw error;
    }
  };

  // دالة للتحقق من العلاقات قبل حذف أمر التوريد
  const checkSupplyOrderRelations = (
    orderId: number,
  ): { canDelete: boolean; reason?: string; relatedOperations?: string[] } => {
    const orderToDelete = supplyOrders.find((o) => o.id === orderId);
    if (!orderToDelete)
      return { canDelete: false, reason: "أمر التوريد غير موجود" };

    // تحويل items إلى array إذا كانت string
    const items = Array.isArray(orderToDelete.items) 
      ? orderToDelete.items 
      : (typeof orderToDelete.items === 'string' 
        ? JSON.parse(orderToDelete.items) 
        : []);

    const imeisInOrder = items.map((item: any) => item.imei).filter(Boolean);
    const relatedOperations: string[] = [];

    // فحص المبيعات - أي مبيعات تحتوي على أجهزة من هذا الأمر
    const relatedSales = sales.filter((sale) => {
      const saleItems = Array.isArray(sale.items) 
        ? sale.items 
        : (typeof sale.items === 'string' 
          ? JSON.parse(sale.items) 
          : []);
      return saleItems.some((item: any) => imeisInOrder.includes(item.deviceId));
    });
    if (relatedSales.length > 0) {
      relatedOperations.push(`${relatedSales.length} فاتورة مبيعات`);
    }

    // فحص المرتجعات - أي مرتجعات تحتوي على أجهزة من هذا الأمر
    const relatedReturns = returns.filter((returnOrder) => {
      const returnItems = Array.isArray(returnOrder.items) 
        ? returnOrder.items 
        : (typeof returnOrder.items === 'string' 
          ? JSON.parse(returnOrder.items) 
          : []);
      return returnItems.some((item: any) => imeisInOrder.includes(item.deviceId));
    });
    if (relatedReturns.length > 0) {
      relatedOperations.push(`${relatedReturns.length} أمر مرتجع`);
    }

    // فحص أوامر التقييم - أي تقييم يحتوي على أجهزة من هذا الأمر
    const relatedEvaluations = evaluationOrders.filter((evalOrder) => {
      const evalItems = Array.isArray(evalOrder.items) 
        ? evalOrder.items 
        : (typeof evalOrder.items === 'string' 
          ? JSON.parse(evalOrder.items) 
          : []);
      return evalItems.some((item: any) => imeisInOrder.includes(item.deviceId));
    });
    if (relatedEvaluations.length > 0) {
      relatedOperations.push(`${relatedEvaluations.length} أمر تقييم`);
    }

    // فحص التحويلات المخزنية - أي تحويل يحتوي على أجهزة من هذا الأمر
    const relatedTransfers = warehouseTransfers.filter((transfer) => {
      const transferItems = Array.isArray(transfer.items) 
        ? transfer.items 
        : (typeof transfer.items === 'string' 
          ? JSON.parse(transfer.items) 
          : []);
      return transferItems.some((item: any) => imeisInOrder.includes(item.deviceId));
    });
    if (relatedTransfers.length > 0) {
      relatedOperations.push(`${relatedTransfers.length} تحويل مخزني`);
    }

    // فحص سجلات الصيانة - أي صيانة لأجهزة من هذا الأمر
    const relatedMaintenance = maintenanceHistory.filter((maintenance) =>
      imeisInOrder.includes(maintenance.deviceId),
    );
    if (relatedMaintenance.length > 0) {
      relatedOperations.push(`${relatedMaintenance.length} سجل صيانة`);
    }

    // فحص أوامر الصيانة - أي أمر صيانة يحتوي على أجهزة من هذا الأمر
    const relatedMaintenanceOrders = maintenanceOrders.filter((maintenanceOrder) => {
      const maintenanceItems = Array.isArray(maintenanceOrder.items) 
        ? maintenanceOrder.items 
        : (typeof maintenanceOrder.items === 'string' 
          ? JSON.parse(maintenanceOrder.items) 
          : []);
      return maintenanceItems.some((item: any) => imeisInOrder.includes(item.imei || item.deviceId));
    });
    if (relatedMaintenanceOrders.length > 0) {
      relatedOperations.push(`${relatedMaintenanceOrders.length} أمر صيانة`);
    }

    // فحص أوامر الاستلام من الصيانة - أي أمر استلام يحتوي على أجهزة من هذا الأمر
    const relatedMaintenanceReceipts = maintenanceReceiptOrders.filter((receiptOrder) => {
      const receiptItems = Array.isArray(receiptOrder.items) 
        ? receiptOrder.items 
        : (typeof receiptOrder.items === 'string' 
          ? JSON.parse(receiptOrder.items) 
          : []);
      return receiptItems.some((item: any) => imeisInOrder.includes(item.imei || item.deviceId));
    });
    if (relatedMaintenanceReceipts.length > 0) {
      relatedOperations.push(`${relatedMaintenanceReceipts.length} أمر استلام صيانة`);
    }

    // فحص أوامر التسليم - أي أمر تسليم يحتوي على أجهزة من هذا الأمر
    const relatedDeliveryOrders = deliveryOrders.filter((deliveryOrder) => {
      const deliveryItems = Array.isArray(deliveryOrder.items) 
        ? deliveryOrder.items 
        : (typeof deliveryOrder.items === 'string' 
          ? JSON.parse(deliveryOrder.items) 
          : []);
      return deliveryItems.some((item: any) => imeisInOrder.includes(item.imei || item.deviceId));
    });
    if (relatedDeliveryOrders.length > 0) {
      relatedOperations.push(`${relatedDeliveryOrders.length} أمر تسليم`);
    }

    // إذا وجدت عمليات مرتبطة، امنع الحذف
    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: "توجد أجهزة من أمر التوريد مستخدمة في عمليات أخرى",
        relatedOperations,
      };
    }

    // يمكن الحذف إذا لم توجد عمليات مرتبطة
    return { canDelete: true };
  };

  // دالة للتحقق من العلاقات قبل حذف فاتورة المبيعات
  const checkSaleRelations = (
    saleId: number,
  ): { canDelete: boolean; reason?: string; relatedOperations?: string[] } => {
    const saleToDelete = sales.find((s) => s.id === saleId);
    if (!saleToDelete)
      return { canDelete: false, reason: "فاتورة المبيعات غير موجودة" };

    const deviceIdsInSale = saleToDelete.items.map((item) => item.deviceId);
    const relatedOperations: string[] = [];

    // فحص المرتجعات - أي مرتجعات تحتوي على أجهزة من هذه الفاتورة
    const relatedReturns = returns.filter((returnOrder) =>
      returnOrder.items.some((item) => deviceIdsInSale.includes(item.deviceId)),
    );
    if (relatedReturns.length > 0) {
      relatedOperations.push(`${relatedReturns.length} أمر مرتجع`);
    }

    // فحص سجلات الصيانة - أي صيانة لأجهزة من هذه الفاتورة
    const relatedMaintenance = maintenanceHistory.filter((maintenance) =>
      deviceIdsInSale.includes(maintenance.deviceId),
    );
    if (relatedMaintenance.length > 0) {
      relatedOperations.push(`${relatedMaintenance.length} سجل صيانة`);
    }

    // فحص أوامر الصيانة - أي أمر صيانة يحتوي على أجهزة من هذه الفاتورة
    const relatedMaintenanceOrders = maintenanceOrders.filter((order) =>
      order.items.some((item) => deviceIdsInSale.includes(item.id)),
    );
    if (relatedMaintenanceOrders.length > 0) {
      relatedOperations.push(`${relatedMaintenanceOrders.length} أمر صيانة`);
    }

    // فحص أوامر التسليم - أي أمر تسليم يحتوي على أجهزة من هذه الفاتورة
    const relatedDeliveryOrders = deliveryOrders.filter((order) =>
      order.items.some((item) => deviceIdsInSale.includes(item.deviceId)),
    );
    if (relatedDeliveryOrders.length > 0) {
      relatedOperations.push(`${relatedDeliveryOrders.length} أمر تسليم`);
    }

    // فحص أوامر استلام الصيانة - أي أمر استلام يحتوي على أجهزة من هذه الفاتورة
    const relatedMaintenanceReceipts = maintenanceReceiptOrders.filter(
      (order) => order.items.some((item) => deviceIdsInSale.includes(item.id)),
    );
    if (relatedMaintenanceReceipts.length > 0) {
      relatedOperations.push(
        `${relatedMaintenanceReceipts.length} أمر استلام صيانة`,
      );
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: "يوجد عمليات مرتبطة بأجهزة هذه الفاتورة",
        relatedOperations,
      };
    }

    return { canDelete: true };
  };

    const deleteSupplyOrder = async (orderId: number) => {
    try {
      // التحقق من العلاقات أولاً
      const relationCheck = checkSupplyOrderRelations(orderId);
      if (!relationCheck.canDelete) {
        throw new Error(
          `لا يمكن حذف أمر التوريد: ${relationCheck.reason}${relationCheck.relatedOperations ? "\nالعمليات المرتبطة: " + relationCheck.relatedOperations.join(", ") : ""}`,
        );
      }

      const orderToDelete = supplyOrders.find((o) => o.id === orderId);
      if (!orderToDelete) return;

      // استخراج قائمة الأجهزة التي سيتم حذفها
      const items = Array.isArray(orderToDelete.items) 
        ? orderToDelete.items 
        : (typeof orderToDelete.items === 'string' 
          ? JSON.parse(orderToDelete.items) 
          : []);
      
      const imeisToRemove = items.map((item: any) => item.imei).filter(Boolean);

      // إرسال طلب الحذف إلى API
      const response = await apiClient.delete(`/api/supply`, { id: orderId });

      if (!response.ok) {
        throw new Error("Failed to delete supply order");
      }

      // تحديث قائمة أوامر التوريد
      setSupplyOrders((prevOrders) => prevOrders.filter((o) => o.id !== orderId));

      // تحديث قائمة الأجهزة من قاعدة البيانات للتأكد من التزامن
      try {
        const devicesResponse = await apiClient.get('/api/devices');
        if (devicesResponse.ok) {
          const updatedDevices = await devicesResponse.json();
          setDevices(updatedDevices);
        }
      } catch (devicesError) {
        console.warn('Failed to refresh devices list after deletion:', devicesError);
        // في حالة فشل تحديث قائمة الأجهزة، نحدثها محلياً
        if (imeisToRemove.length > 0) {
          setDevices((prevDevices) =>
            prevDevices.filter((device) => !imeisToRemove.includes(device.id)),
          );
        }
      }

      const supplier = suppliers.find((s) => s.id === orderToDelete.supplierId);
      addActivity({
        type: "supply",
        description: `تم حذف أمر التوريد ${orderToDelete.supplyOrderId} و ${imeisToRemove.length} أجهزة من المورد ${supplier?.name || "غير معروف"}`,
      });
    } catch (error) {
      console.error("Failed to delete supply order:", error);
      throw error;
    }
  };

  // Evaluation Orders
  const addEvaluationOrder = async (
    order: Omit<EvaluationOrder, "id" | "createdAt">,
  ) => {
    try {
      // إرسال الأمر إلى API
      const response = await apiClient.post('/api/evaluations', {
        ...order,
        date: order.date || new Date().toISOString(),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create evaluation order');
      }

      // استقبال الأمر الذي تم إنشاؤه من API
      const newOrder = await response.json();

      // تحديث حالة التطبيق
      setEvaluationOrders((prev) => [newOrder, ...prev]);

      // Update device status based on evaluation
      newOrder.items.forEach((item: any) => {
        let newStatus: DeviceStatus;
        switch (item.finalGrade) {
          case "جاهز للبيع":
            newStatus = "متاح للبيع";
            break;
          case "يحتاج صيانة":
            newStatus = "تحتاج صيانة";
            break;
          case "عيب فني":
            newStatus = "معيب";
            break;
          case "تالف":
            newStatus = "تالف";
            break;
          default:
            return;
        }
        updateDeviceStatus(item.deviceId, newStatus);
      });

      addActivity({
        type: "evaluation",
        description: `تم تقييم ${newOrder.items.length} أجهزة في الأمر ${newOrder.orderId}`,
      });
      
      return newOrder;
    } catch (error) {
      console.error('Failed to add evaluation order:', error);
      addActivity({
        type: "evaluation",
        description: `⚠️ فشل في إنشاء أمر تقييم: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  

  const updateEvaluationOrder = async (updatedOrder: EvaluationOrder) => {
    try {
      // إرسال التحديث إلى API
      const response = await apiClient.put("/api/evaluations", updatedOrder);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update evaluation order');
      }

      // استقبال الأمر المحدث من API
      const updated = await response.json();

      // تحديث حالة التطبيق
      setEvaluationOrders((prev) =>
        prev.map((o) => (o.id === updated.id ? updated : o)),
      );

      // Update device status based on evaluation
      updated.items.forEach((item: any) => {
        let newStatus: DeviceStatus;
        switch (item.finalGrade) {
          case "جاهز للبيع":
            newStatus = "متاح للبيع";
            break;
          case "يحتاج صيانة":
            newStatus = "تحتاج صيانة";
            break;
          case "عيب فني":
            newStatus = "معيب";
            break;
          case "تالف":
            newStatus = "تالف";
            break;
          default:
            return;
        }
        updateDeviceStatus(item.deviceId, newStatus);
      });

      addActivity({
        type: "evaluation",
        description: `تم تحديث أمر التقييم ${updated.orderId}`,
      });
      
      return updated;
    } catch (error) {
      console.error('Failed to update evaluation order:', error);
      addActivity({
        type: "evaluation",
        description: `⚠️ فشل في تحديث أمر تقييم: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  

  const deleteEvaluationOrder = async (orderId: number) => {
    try {
      // إرسال طلب الحذف إلى API
      const response = await apiClient.delete("/api/evaluations", { id: orderId });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete evaluation order');
      }

      // تحديث حالة التطبيق
      setEvaluationOrders((prev) => prev.filter((o) => o.id !== orderId));

      addActivity({
        type: "evaluation",
        description: `تم حذف أمر التقييم رقم ${orderId}`,
      });
    } catch (error) {
      console.error('Failed to delete evaluation order:', error);
      addActivity({
        type: "evaluation",
        description: `⚠️ فشل في حذف أمر تقييم: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  const checkEvaluationOrderRelations = (orderId: number) => {
    // البحث عن الأمر المراد حذفه
    const evaluationOrder = evaluationOrders.find(order => order.id === orderId);
    if (!evaluationOrder) {
      return { canDelete: false, reason: 'أمر التقييم غير موجود' };
    }

    // التحقق من وجود أوامر صيانة مرتبطة بالأجهزة في أمر التقييم
    const relatedOperations: string[] = [];
    
    // فحص أوامر الصيانة اللاحقة
    const relatedMaintenanceOrders = maintenanceOrders.filter(maintenanceOrder => {
      const maintenanceDate = new Date(maintenanceOrder.date);
      const evaluationDate = new Date(evaluationOrder.date);
      
      return maintenanceDate > evaluationDate && 
             maintenanceOrder.items.some((item: any) => 
               evaluationOrder.items.some(evalItem => evalItem.deviceId === item.deviceId)
             );
    });

    if (relatedMaintenanceOrders.length > 0) {
      relatedMaintenanceOrders.forEach(order => {
        relatedOperations.push(`أمر صيانة ${order.orderNumber}`);
      });
    }

    // فحص أوامر التسليم اللاحقة
    const relatedDeliveryOrders = deliveryOrders.filter(deliveryOrder => {
      const deliveryDate = new Date(deliveryOrder.date);
      const evaluationDate = new Date(evaluationOrder.date);
      
      return deliveryDate > evaluationDate && 
             deliveryOrder.items.some((item: any) => 
               evaluationOrder.items.some(evalItem => evalItem.deviceId === item.deviceId)
             );
    });

    if (relatedDeliveryOrders.length > 0) {
      relatedDeliveryOrders.forEach(order => {
        relatedOperations.push(`أمر تسليم ${order.orderNumber}`);
      });
    }

    // فحص عمليات البيع اللاحقة
    const relatedSales = sales.filter(sale => {
      const saleDate = new Date(sale.saleDate);
      const evaluationDate = new Date(evaluationOrder.date);
      
      return saleDate > evaluationDate && 
             sale.items.some(item => 
               evaluationOrder.items.some(evalItem => evalItem.deviceId === item.deviceId)
             );
    });

    if (relatedSales.length > 0) {
      relatedSales.forEach(sale => {
        relatedOperations.push(`عملية بيع ${sale.invoiceNumber}`);
      });
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: 'لا يمكن حذف أمر التقييم لأنه مرتبط بعمليات لاحقة',
        relatedOperations
      };
    }

    return { canDelete: true };
  };

  // Maintenance Log
  const addMaintenanceLog = (log: Omit<MaintenanceLog, "status">) => {
    const newLog: MaintenanceLog = { ...log, status: "pending" };
    setMaintenanceHistory((prev) => [newLog, ...prev]);
  };

  const acknowledgeMaintenanceLog = (deviceId: string) => {
    setMaintenanceHistory((prev) => {
      const historyCopy = [...prev];
      const logIndex = historyCopy.findIndex(
        (log) => log.deviceId === deviceId && log.status === "pending",
      );
      if (logIndex !== -1) {
        const device = devices.find((d) => d.id === deviceId);
        const warehouse = warehouses.find((w) => w.id === device?.warehouseId);
        historyCopy[logIndex] = {
          ...historyCopy[logIndex],
          status: "acknowledged",
          acknowledgedDate: new Date().toISOString(),
          warehouseName: warehouse?.name || "غير معروف",
          acknowledgedBy: "مدير النظام",
        };
      }
      return historyCopy;
    });
  };

  const revertDeviceToMaintenance = (deviceId: string) => {
    const device = devices.find((d) => d.id === deviceId);
    if (!device) return;

    // 1. Change device status back to "قيد الإصلاح"
    updateDeviceStatus(deviceId, "قيد الإصلاح");

    // 2. Remove the corresponding "pending" log from maintenanceHistory
    setMaintenanceHistory((prev) =>
      prev.filter(
        (log) => !(log.deviceId === deviceId && log.status === "pending"),
      ),
    );

    addActivity({
      type: "maintenance",
      description: `تمت استعادة الجهاز ${device.model} إلى الصيانة`,
    });
  };

  // Warehouse Transfers
  const addWarehouseTransfer = async (
    transfer: Omit<WarehouseTransfer, "id" | "transferNumber" | "createdAt">,
  ) => {
    try {
      // إعداد البيانات الأساسية
      const transferNumber = `WT-${Date.now()}`;
      
      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إنشاء /api/warehouse-transfers endpoint
      
      const newId = Math.max(0, ...warehouseTransfers.map((t) => t.id)) + 1;
      const newTransfer: WarehouseTransfer = {
        ...transfer,
        id: newId,
        transferNumber,
      };

      setWarehouseTransfers((prev) => [newTransfer, ...prev]);

      // تحديث حالة الأجهزة
      transfer.items.forEach((item) => {
        updateDeviceStatus(item.deviceId, "مرسل للمخزن");
      });

      addActivity({
        type: "supply",
        description: `تم إنشاء تحويل مخزني جديد: ${transferNumber}`,
      });
      
      return newTransfer;
    } catch (error) {
      console.error('Failed to add warehouse transfer:', error);
      addActivity({
        type: "supply",
        description: `⚠️ فشل في إنشاء تحويل مخزني: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  const updateWarehouseTransfer = (updatedTransfer: WarehouseTransfer) => {
    setWarehouseTransfers((prev) =>
      prev.map((t) => (t.id === updatedTransfer.id ? updatedTransfer : t)),
    );
    addActivity({
      type: "transfer",
      description: `تم تحديث أمر التحويل ${updatedTransfer.transferNumber}`,
    });
  };

  const deleteWarehouseTransfer = async (transferId: number) => {
    try {
      // حالياً نحفظ محلياً حتى يتم إنشاء API
      // TODO: إرسال إلى /api/warehouse-transfers

      setWarehouseTransfers((prev) => prev.filter((t) => t.id !== transferId));

      addActivity({
        type: "supply",
        description: `تم حذف التحويل المخزني رقم ${transferId}`,
      });
    } catch (error) {
      console.error('Failed to delete warehouse transfer:', error);
      addActivity({
        type: "supply",
        description: `⚠️ فشل في حذف التحويل المخزني: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  const completeWarehouseTransfer = (transferId: number) => {
    const transfer = warehouseTransfers.find((t) => t.id === transferId);
    if (!transfer) return;

    setWarehouseTransfers((prev) =>
      prev.map((t) =>
        t.id === transferId ? { ...t, status: "completed" } : t,
      ),
    );

    setDevices((prevDevices) =>
      prevDevices.map((device) => {
        if (transfer.items.some((item) => item.deviceId === device.id)) {
          return {
            ...device,
            status: "متاح للبيع",
            warehouseId: transfer.toWarehouseId,
          };
        }
        return device;
      }),
    );

    addActivity({
      type: "transfer",
      description: `تم استلام أمر التحويل ${transfer.transferNumber} في مخزن ${transfer.toWarehouseName}`,
    });
  };

  // User CRUD
  const addUser = async (user: Omit<User, "id">) => {
    try {
      const { apiClient, handleApiResponse } = await import('@/lib/api-client');
      
      const response = await apiClient.post("/api/users", user);
      const newUser = await handleApiResponse(response) as User;

      setUsers((prev) => [newUser, ...prev].sort((a, b) => b.id - a.id));

      addActivity({
        type: "supply",
        description: `تم إضافة مستخدم جديد: ${newUser.name}`,
      });
    } catch (error) {
      console.error("Failed to add user:", error);
      throw error;
    }
  };

  const updateUser = async (updatedUser: User) => {
    try {
      const { apiClient, handleApiResponse } = await import('@/lib/api-client');
      
      const response = await apiClient.put("/api/users", updatedUser);
      const user = await handleApiResponse(response);
      
      setUsers((prev) => prev.map((u) => (u.id === user.id ? user : u)));

      addActivity({
        type: "supply",
        description: `تم تحديث المستخدم: ${user.name}`,
      });
    } catch (error) {
      console.error("Failed to update user:", error);
      throw error;
    }
  };

  const deleteUser = async (userId: number) => {
    try {
      const { apiClient, handleApiResponse } = await import('@/lib/api-client');
      
      const response = await apiClient.delete("/api/users", { id: userId });
      await handleApiResponse(response);

      const userToDelete = users.find((u) => u.id === userId);
      setUsers((prev) => prev.filter((u) => u.id !== userId));

      addActivity({
        type: "supply",
        description: `تم حذف المستخدم: ${userToDelete?.name || "غير معروف"}`,
      });
    } catch (error) {
      console.error("Failed to delete user:", error);
      throw error;
    }
  };

  const setCurrentUserFunc = (user: User | null) => {
    setCurrentUser(user);
  };

  const updateSystemSettings = async (settings: SystemSettings) => {
    try {
      // إرسال التحديث إلى API
      const response = await apiClient.put("/api/settings", settings);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update system settings');
      }

      // استقبال الإعدادات المحدثة من API
      const updatedSettings = await response.json();

      // تحديث حالة التطبيق
      setSystemSettings(updatedSettings);

      addActivity({
        type: "supply",
        description: "تم تحديث إعدادات النظام بنجاح",
      });
      
      return updatedSettings;
    } catch (error) {
      console.error('Failed to update system settings:', error);
      addActivity({
        type: "supply",
        description: `⚠️ فشل في تحديث إعدادات النظام: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  const getFilteredStocktakes = (filter: StocktakeFilter) => {
    return stocktakes.filter((stocktake) => {
      if (filter.status && stocktake.status !== filter.status) return false;
      return true;
    });
  };

  // Employee Requests
  const addEmployeeRequest = async (
    request: Omit<
      EmployeeRequest,
      | "id"
      | "requestNumber"
      | "status"
      | "requestDate"
      | "employeeName"
      | "employeeId"
    >,
  ) => {
    try {
      const employeeName = currentUser?.name || "مستخدم غير معروف";
      const employeeId = currentUser?.id || 0;
      
      const newRequestData = {
        ...request,
        employeeName: employeeName,
        employeeId: employeeId,
        status: "قيد المراجعة" as EmployeeRequestStatus,
        requestDate: new Date().toISOString(),
      };

      // إرسال إلى API
      const response = await apiClient.post('/api/employee-requests', newRequestData, {
        headers: getAuthHeader()
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create employee request: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      const newRequest = result.request;

      // تحديث الحالة المحلية
      setEmployeeRequests((prev) => [newRequest, ...prev]);

      addActivity({
        type: "request",
        description: `تم إنشاء طلب جديد ${newRequest.requestNumber} - ${newRequest.requestType}`,
      });

      return newRequest;
    } catch (error) {
      console.error('Failed to add employee request:', error);
      addActivity({
        type: "request",
        description: `⚠️ فشل في إرسال الطلب: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  

  const processEmployeeRequest = async (
    requestId: number,
    status: "approved" | "rejected",
    adminNotes: string = "",
  ) => {
    try {
      const request = employeeRequests.find((r) => r.id === requestId);
      if (!request) {
        throw new Error('الطلب غير موجود');
      }

      // إرسال إلى API
      const updateData = {
        id: requestId,
        status: status === "approved" ? "تم التنفيذ" : "مرفوض",
        adminNotes,
        processedBy: currentUser?.id || 0,
      };

      const response = await apiClient.put('/api/employee-requests', updateData, {
        headers: getAuthHeader()
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update employee request: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      const updatedRequest = result.request;

      // تحديث الحالة المحلية
      setEmployeeRequests((prev) =>
        prev.map((r) => (r.id === requestId ? updatedRequest : r)),
      );

      addActivity({
        type: "request",
        description: `تم ${status === "approved" ? "قبول" : "رفض"} الطلب ${request.requestNumber}`,
      });
      
      return updatedRequest;
    } catch (error) {
      console.error('Failed to process employee request:', error);
      addActivity({
        type: "request",
        description: `⚠️ فشل في معالجة الطلب: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  // Internal Messaging
  const sendMessage = async (
    message: Omit<
      InternalMessage,
      | "id"
      | "threadId"
      | "senderId"
      | "senderName"
      | "sentDate"
      | "status"
      | "isRead"
    > & { recipientIds?: number[] },
  ) => {
    if (!currentUser) return;

    const { recipientIds, ...messageData } = message;
    const newId =
      (internalMessages.length > 0
        ? Math.max(...internalMessages.map((m) => m.id))
        : 0) + 1;

    // Determine the threadId
    let threadId: number;

    if (message.parentMessageId) {
      // It's a reply, use the parent's threadId
      const parentMessage = internalMessages.find(
        (m) => m.id === message.parentMessageId,
      );
      threadId = parentMessage?.threadId || newId; // Fallback to newId if parent not found
    } else if (recipientIds && recipientIds.length > 0) {
      // It's a new group message
      threadId = newId;
    } else {
      // It's a new direct message
      const existingThread = internalMessages.find(
        (m) =>
          (m.senderId === currentUser.id &&
            m.recipientId === message.recipientId) ||
          (m.senderId === message.recipientId &&
            m.recipientId === currentUser.id &&
            (!m.recipientIds || m.recipientIds.length === 0)),
      );
      threadId = existingThread?.threadId || newId;
    }

    try {
      if (recipientIds && recipientIds.length > 0) {
        // Group message logic
        const newMessages: InternalMessage[] = recipientIds.map(
          (recipientId, index) => {
            const recipient = users.find((u) => u.id === recipientId);
            return {
              id: newId + index,
              threadId: threadId,
              senderId: currentUser.id,
              senderName: currentUser.name,
              sentDate: new Date().toISOString(),
              status: "مرسلة",
              isRead: false,
              ...messageData,
              recipientId: recipientId,
              recipientName: recipient?.name || "",
              recipientIds: recipientIds.filter((id) => id !== recipientId), // Store other recipients
            };
          },
        );

        // حفظ كل رسالة في قاعدة البيانات
        for (const msg of newMessages) {
          await apiClient.post('/api/internal-messages', msg, {
            headers: getAuthHeader()
          });
        }

        setInternalMessages((prev) => [...newMessages, ...prev]);
      } else {
        // Single message logic
        const recipient = users.find((u) => u.id === message.recipientId);
        const newMessage: InternalMessage = {
          id: newId,
          threadId: threadId,
          senderId: currentUser.id,
          senderName: currentUser.name,
          sentDate: new Date().toISOString(),
          status: "مرسلة",
          isRead: false,
          ...messageData,
          recipientName: message.recipientName || recipient?.name || "الكل",
        };

        // حفظ الرسالة في قاعدة البيانات
        await apiClient.post('/api/internal-messages', newMessage, {
          headers: getAuthHeader()
        });

        setInternalMessages((prev) => [newMessage, ...prev]);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      addActivity({
        type: "message",
        description: `⚠️ فشل في إرسال الرسالة: ${error instanceof Error ? error.message : String(error)}`,
      });
    }
  };

  const updateMessage = async (
    messageId: number,
    updates: Partial<InternalMessage>
  ) => {
    try {
      // إرسال إلى API
      const updateData = {
        id: messageId,
        ...updates
      };

      const response = await apiClient.put('/api/internal-messages', updateData, {
        headers: getAuthHeader()
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update internal message: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      const updatedMessage = result.message;

      // تحديث الحالة المحلية
      setInternalMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId ? updatedMessage : msg,
        ),
      );

      addActivity({
        type: "message",
        description: `تم تحديث الرسالة رقم ${messageId}`,
      });
    } catch (error) {
      console.error('Failed to update message:', error);
      addActivity({
        type: "message",
        description: `⚠️ فشل في تحديث الرسالة: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  }

  const addInternalMessage = async (
    message: Omit<InternalMessage, "id" | "createdAt">
  ) => {
    try {
      // إرسال إلى API
      const response = await apiClient.post('/api/internal-messages', message, {
        headers: getAuthHeader()
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create internal message: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      const newMessage = result.message;

      // تحديث الحالة المحلية
      setInternalMessages((prev) => [newMessage, ...prev]);

      addActivity({
        type: "message",
        description: `تم إرسال رسالة جديدة من ${newMessage.senderName}`,
      });

      return newMessage;
    } catch (error) {
      console.error('Failed to add internal message:', error);
      addActivity({
        type: "message",
        description: `⚠️ فشل في إرسال الرسالة: ${error instanceof Error ? error.message : String(error)}`,
      });
      throw error;
    }
  };

  // دالة لإنشاء header التفويض
  const getAuthHeader = () => {
    if (!currentUser) {
      // استخدام البيانات الافتراضية للمستخدم المديري
      return { 'Authorization': `Bearer ${btoa('user:admin:admin')}` };
    }
    // استخدام بيانات المستخدم الحالي
    const token = btoa(`user:${currentUser.username || currentUser.name}:admin`);
    return { 'Authorization': `Bearer ${token}` };
  };

  const value = {
    isLoading,
    devices,
    clients,
    suppliers,
    warehouses,
    sales,
    returns,
    activities,
    manufacturers,
    deviceModels,
    supplyOrders,
    evaluationOrders,
    maintenanceHistory,
    warehouseTransfers,
    users,
    currentUser,
    systemSettings,
    employeeRequests,
    internalMessages,
    acceptanceOrders,
    maintenanceOrders,
    deliveryOrders,
    maintenanceReceiptOrders,
    deviceReturnHistory,
    stocktakes,
    addDevice,
    updateDevice,
    deleteDevice,
    updateDeviceStatus,
    getDevicesByStatus,
    getDevicesByIds,
    addContact,
    updateContact,
    deleteContact,
    checkClientRelations,
    checkSupplierRelations,
    addWarehouse,
    updateWarehouse,
    deleteWarehouse,
    checkWarehouseRelations,
    addSale,
    updateSale,
    deleteSale,
    checkSaleRelations,
    addReturn,
    updateReturn,
    deleteReturn,
    isDeviceReturned,
    canDeviceBeReturned,
    addDeviceReturnHistory,
    addManufacturer,
    addDeviceModel,
    addSupplyOrder,
    updateSupplyOrder,
    deleteSupplyOrder,
    checkSupplyOrderRelations,
    addEvaluationOrder,
    updateEvaluationOrder,
    deleteEvaluationOrder,
    checkEvaluationOrderRelations,
    addMaintenanceLog,
    acknowledgeMaintenanceLog,
    revertDeviceToMaintenance,
    addWarehouseTransfer,
    updateWarehouseTransfer,
    deleteWarehouseTransfer,
    completeWarehouseTransfer,
    addUser,
    updateUser,
    deleteUser,
    setCurrentUser: setCurrentUserFunc,
    updateSystemSettings,
    addEmployeeRequest,
    processEmployeeRequest,
    sendMessage,
    updateMessage,
    addAcceptanceOrder,
    updateAcceptanceOrder,
    deleteAcceptanceOrder,
    addMaintenanceOrder,
    updateMaintenanceOrder,
    deleteMaintenanceOrder,
    checkMaintenanceOrderRelations,
    checkDeviceRelationsInMaintenance,
    addDeliveryOrder,
    updateDeliveryOrder,
    deleteDeliveryOrder,
    checkDeliveryOrderRelations,
    addMaintenanceReceiptOrder,
    updateMaintenanceReceiptOrder,
    deleteMaintenanceReceiptOrder,
    addStocktake,
    updateStocktake,
    deleteStocktake,
    getFilteredStocktakes,
    addStocktakeItem,
    updateStocktakeItem,
    addStocktakeDiscrepancy,
    resolveStocktakeDiscrepancy,
    changeStocktakeStatus,
    reviewStocktake,
    // Backup and restore functions
    createBackupSnapshot,
    restoreFromSnapshot,
    exportStoreData,
    importStoreData,
    getAuthHeader,
    addActivity,
  };

  // Backup and restore functions
  function createBackupSnapshot() {
    return {
      timestamp: new Date().toISOString(),
      data: {
        devices,
        clients,
        suppliers,
        warehouses,
        sales,
        returns,
        manufacturers,
        deviceModels,
        supplyOrders,
        evaluationOrders,
        maintenanceOrders,
        maintenanceHistory,
        warehouseTransfers,
        deliveryOrders,
        employeeRequests,
        internalMessages,
        stocktakes,
        systemSettings,
        currentUser,
        users, // Ensure users are included in the backup
        activities,
        deviceReturnHistory,
        acceptanceOrders,
      },
    };
  }

  function restoreFromSnapshot(snapshot: any) {
    if (!snapshot || typeof snapshot !== "object") {
      console.error("Invalid backup snapshot - not an object");
      return;
    }

    let data = snapshot;

    // Handle wrapped data format
    if (snapshot.data && typeof snapshot.data === "object") {
      data = snapshot.data;
    }

    try {
      // Restore all state with fallbacks for missing data
      if (data.devices !== undefined)
        setDevices(Array.isArray(data.devices) ? data.devices : []);
      if (data.clients !== undefined)
        setClients(Array.isArray(data.clients) ? data.clients : []);
      if (data.suppliers !== undefined)
        setSuppliers(Array.isArray(data.suppliers) ? data.suppliers : []);
      if (data.warehouses !== undefined)
        setWarehouses(Array.isArray(data.warehouses) ? data.warehouses : []);
      if (data.sales !== undefined)
        setSales(Array.isArray(data.sales) ? data.sales : []);
      if (data.returns !== undefined)
        setReturns(Array.isArray(data.returns) ? data.returns : []);
      if (data.manufacturers !== undefined)
        setManufacturers(
          Array.isArray(data.manufacturers) ? data.manufacturers : [],
        );
      if (data.deviceModels !== undefined)
        setDeviceModels(
          Array.isArray(data.deviceModels) ? data.deviceModels : [],
        );
      if (data.supplyOrders !== undefined)
        setSupplyOrders(
          Array.isArray(data.supplyOrders) ? data.supplyOrders : [],
        );
      if (data.evaluationOrders !== undefined)
        setEvaluationOrders(
          Array.isArray(data.evaluationOrders) ? data.evaluationOrders : [],
        );
      if (data.maintenanceOrders !== undefined)
        setMaintenanceOrders(
          Array.isArray(data.maintenanceOrders) ? data.maintenanceOrders : [],
        );
      if (data.maintenanceHistory !== undefined)
        setMaintenanceHistory(
          Array.isArray(data.maintenanceHistory) ? data.maintenanceHistory : [],
        );
      if (data.warehouseTransfers !== undefined)
        setWarehouseTransfers(
          Array.isArray(data.warehouseTransfers) ? data.warehouseTransfers : [],
        );
      if (data.deliveryOrders !== undefined)
        setDeliveryOrders(
          Array.isArray(data.deliveryOrders) ? data.deliveryOrders : [],
        );
      if (data.employeeRequests !== undefined)
        setEmployeeRequests(
          Array.isArray(data.employeeRequests) ? data.employeeRequests : [],
        );
      if (data.internalMessages !== undefined)
        setInternalMessages(
          Array.isArray(data.internalMessages) ? data.internalMessages : [],
        );
      if (data.stocktakes !== undefined)
        setStocktakes(Array.isArray(data.stocktakes) ? data.stocktakes : []);
      if (data.users !== undefined)
        setUsers(Array.isArray(data.users) ? data.users : []);
      if (data.activities !== undefined)
        setActivities(Array.isArray(data.activities) ? data.activities : []);
      if (data.deviceReturnHistory !== undefined)
        setDeviceReturnHistory(
          Array.isArray(data.deviceReturnHistory)
            ? data.deviceReturnHistory
            : [],
        );
      if (data.acceptanceOrders !== undefined)
        setAcceptanceOrders(
          Array.isArray(data.acceptanceOrders) ? data.acceptanceOrders : [],
        );

      // Restore settings and user if available
      if (data.systemSettings && typeof data.systemSettings === "object") {
        setSystemSettings(data.systemSettings);
      }

      if (data.currentUser && typeof data.currentUser === "object") {
        setCurrentUser(data.currentUser);
      }

      console.log("Successfully restored data from backup");
    } catch (error) {
      console.error("Error during restore:", error);
      throw new Error(
        "Failed to restore backup data: " +
          (error instanceof Error ? error.message : "Unknown error"),
      );
    }
  }

  function exportStoreData() {
    return createBackupSnapshot();
  }

  function importStoreData(data: any) {
    restoreFromSnapshot(data);
  }

  
  // تحديث البيانات القديمة لتتضمن الختم الزمني
  useEffect(() => {
    setSales((prevSales) =>
      prevSales.map((sale) => ({
        ...sale,
        createdAt: sale.createdAt || sale.date + "T00:00:00.000Z",
        employeeName: sale.employeeName || "مدير النظام",
      })),
    );
  }, []);

  return (
    <StoreContext.Provider value={value}>{children}</StoreContext.Provider>
  );
}

export function useStore() {
  const context = useContext(StoreContext);
  if (context === undefined) {
    // Return a safe default instead of throwing error during development
    console.warn(
      "useStore called outside of StoreProvider, returning defaults",
    );

    // Create default permissions with all options enabled
    const createDefaultPermissions = () => {
      const permissions = {} as AppPermissions;
      permissionPages.forEach((page) => {
        permissions[page] = {
          view: true,
          create: true,
          edit: true,
          delete: true,
          viewAll: true,
          manage: [1, 2, 3],
          acceptWithoutWarranty: true,
        };
      });
      return permissions;
    };

    return {
      isLoading: false, // Changed to false to prevent loading state
      devices: [],
      clients: [],
      suppliers: [],
      warehouses: [],
      sales: [],
      returns: [],
      activities: [],
      manufacturers: [],
      deviceModels: [],
      supplyOrders: [],
      evaluationOrders: [],
      maintenanceHistory: [],
      warehouseTransfers: [],
      users: [],
      currentUser: {
        id: 1,
        name: "مدير النظام",
        username: "admin",
        email: "<EMAIL>",
        permissions: createDefaultPermissions(),
      },
      systemSettings: {
        id: 1,
        logoUrl: "",
        companyNameAr: "",
        companyNameEn: "",
        addressAr: "",
        addressEn: "",
        phone: "",
        email: "",
        website: "",
        footerTextAr: "",
        footerTextEn: "",
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      employeeRequests: [],
      internalMessages: [],
      acceptanceOrders: [],
      maintenanceOrders: [],
      deliveryOrders: [],
      maintenanceReceiptOrders: [],
      deviceReturnHistory: [],
      stocktakes: [],
      addDevice: () => {},
      updateDevice: () => {},
      deleteDevice: () => {},
      updateDeviceStatus: () => {},
      getDevicesByStatus: () => [],
      addContact: () => Promise.resolve(),
      updateContact: () => Promise.resolve(),
      deleteContact: () => Promise.resolve(),
      checkClientRelations: () => ({ canDelete: true }),
      checkSupplierRelations: () => ({ canDelete: true }),
      addWarehouse: () => Promise.resolve(),
      updateWarehouse: () => Promise.resolve(),
      deleteWarehouse: () => Promise.resolve(),
      checkWarehouseRelations: () => ({ canDelete: true }),
      addSale: () => Promise.resolve(),
      updateSale: () => {},
      deleteSale: () => {},
      checkSaleRelations: () => ({ canDelete: true }),
      addReturn: () => Promise.resolve(),
      updateReturn: () => Promise.resolve(),
      deleteReturn: () => Promise.resolve(),
      isDeviceReturned: () => false,
      canDeviceBeReturned: () => ({ canReturn: false }),
      addDeviceReturnHistory: () => {},
      addManufacturer: () => ({ id: 0, name: "" }),
      addDeviceModel: () => {},
      addSupplyOrder: () => Promise.resolve(),
      updateSupplyOrder: () => Promise.resolve(),
      deleteSupplyOrder: () => {},
      checkSupplyOrderRelations: () => ({ canDelete: true }),
      addEvaluationOrder: () => {},
      updateEvaluationOrder: () => {},
      deleteEvaluationOrder: () => {},
      checkEvaluationOrderRelations: () => ({ canDelete: true }),
      getDevicesByIds: () => [],
      addMaintenanceLog: () => {},
      acknowledgeMaintenanceLog: () => {},
      revertDeviceToMaintenance: () => {},
      addWarehouseTransfer: () => {},
      updateWarehouseTransfer: () => {},
      deleteWarehouseTransfer: () => {},
      completeWarehouseTransfer: () => {},
      addUser: () => {
        console.warn("addUser called outside of StoreProvider context - using fallback");
        return Promise.resolve();
      },
      updateUser: () => Promise.resolve(),
      deleteUser: () => Promise.resolve(),
      setCurrentUser: () => {},
      updateSystemSettings: () => {},
      addEmployeeRequest: () => {},
      processEmployeeRequest: () => {},
      sendMessage: () => {},
      updateMessage: () => {},
      addAcceptanceOrder: () => {},
      updateAcceptanceOrder: () => {},
      deleteAcceptanceOrder: () => {},
      addMaintenanceOrder: () => {},
      updateMaintenanceOrder: () => {},
      deleteMaintenanceOrder: () => {},
      checkMaintenanceOrderRelations: () => ({ canDelete: true }),
      checkDeviceRelationsInMaintenance: () => ({
        hasRelations: false,
        relatedOperations: [],
      }),
      addDeliveryOrder: () => {},
      updateDeliveryOrder: () => {},
      deleteDeliveryOrder: () => {},
      checkDeliveryOrderRelations: () => ({ canDelete: true }),
      addMaintenanceReceiptOrder: () => {},
      updateMaintenanceReceiptOrder: () => {},
      deleteMaintenanceReceiptOrder: () => {},
      addStocktake: () => {},
      updateStocktake: () => {},
      deleteStocktake: () => {},
      getFilteredStocktakes: () => [],
      addStocktakeItem: () => {},
      updateStocktakeItem: () => {},
      addStocktakeDiscrepancy: () => {},
      resolveStocktakeDiscrepancy: () => {},
      changeStocktakeStatus: () => {},
      reviewStocktake: () => {},
      createBackupSnapshot: () => ({}),
      restoreFromSnapshot: () => {},
      exportStoreData: () => ({}),
      importStoreData: () => {},
    };
  }
  return context;
}
