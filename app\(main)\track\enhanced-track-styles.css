/* Enhanced Track Page Styles */

/* تحسينات عامة للصفحة */
.track-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 1rem;
}

/* تحسينات بطاقة البحث */
.search-card {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.search-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* تحسينات بطاقة تفاصيل الجهاز */
.device-details-card {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.device-details-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* تحسينات السجل التاريخي */
.device-history-timeline {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.timeline-event-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.timeline-event-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.timeline-event-card:hover::before {
  transform: scaleX(1);
}

.timeline-event-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* تحسينات الأيقونات */
.icon-bounce {
  animation: iconBounce 2s infinite;
}

@keyframes iconBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-2px);
  }
}

/* تحسينات الألوان حسب نوع العملية */
.timeline-supply {
  border-left: 4px solid #06b6d4;
}

.timeline-evaluation {
  border-left: 4px solid #6366f1;
}

.timeline-maintenance {
  border-left: 4px solid #eab308;
}

.timeline-transfer {
  border-left: 4px solid #6b7280;
}

.timeline-sale {
  border-left: 4px solid #10b981;
}

.timeline-return {
  border-left: 4px solid #ef4444;
}

.timeline-replacement {
  border-left: 4px solid #3b82f6;
}

.timeline-receipt {
  border-left: 4px solid #8b5cf6;
}

/* تحسينات الشارات */
.badge-supply {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  border: none;
}

.badge-evaluation {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  border: none;
}

.badge-maintenance {
  background: linear-gradient(135deg, #eab308, #d97706);
  color: white;
  border: none;
}

.badge-transfer {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
  border: none;
}

.badge-sale {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
}

.badge-return {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
}

.badge-replacement {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
}

.badge-receipt {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border: none;
}

/* تحسينات الرسوم المتحركة */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .track-page {
    padding: 0.5rem;
  }
  
  .device-details-card .grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .timeline-event-card {
    margin-bottom: 1rem;
  }
  
  .timeline-event-card .flex {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

@media (max-width: 640px) {
  .search-card .flex {
    flex-direction: column;
    gap: 1rem;
  }
  
  .timeline-event-card .grid {
    grid-template-columns: 1fr;
  }
}

/* تحسينات الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .track-page {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }
  
  .search-card,
  .device-details-card,
  .device-history-timeline {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .timeline-event-card {
    background: rgba(30, 41, 59, 0.6);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .timeline-event-card:hover {
    background: rgba(30, 41, 59, 0.8);
  }
}

/* تحسينات التفاعل */
.interactive-element {
  transition: all 0.2s ease;
  cursor: pointer;
}

.interactive-element:hover {
  transform: scale(1.02);
}

.interactive-element:active {
  transform: scale(0.98);
}

/* تحسينات التركيز */
.focus-ring:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* تحسينات التحميل */
.loading-shimmer {
  background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* تحسينات الطباعة */
@media print {
  .track-page {
    background: white;
    padding: 0;
  }
  
  .search-card,
  .timeline-event-card {
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
  
  .icon-bounce {
    animation: none;
  }
}

/* تحسينات إضافية للتفاعل */
.pulse-on-hover:hover {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.glow-effect {
  position: relative;
  overflow: hidden;
}

.glow-effect::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glow-effect:hover::after {
  opacity: 1;
}
